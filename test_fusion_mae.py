import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
import os
from datetime import datetime

# 导入我们的模块
from fusion_mae import create_fusion_mae_model, FusionMAE
from utils.dataset import FusionMAEDataset
from utils.loss import FusionMAELoss

def test_model_creation():
    """测试模型创建"""
    print("=" * 50)
    print("测试1: 模型创建")
    print("=" * 50)
    
    try:
        model = create_fusion_mae_model(
            img_size=256,
            patch_size=16,
            sar_channels=1,
            ms_channels=5
        )
        
        param_count = sum(p.numel() for p in model.parameters() if p.requires_grad)
        print(f"✓ 模型创建成功")
        print(f"✓ 模型参数数量: {param_count:,}")
        print(f"✓ 模型类型: {type(model).__name__}")
        
        return True, model
    except Exception as e:
        print(f"✗ 模型创建失败: {str(e)}")
        return False, None

def test_forward_pass(model):
    """测试前向传播"""
    print("\n" + "=" * 50)
    print("测试2: 前向传播")
    print("=" * 50)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    
    try:
        # 创建测试数据
        batch_size = 2
        sar_img = torch.randn(batch_size, 1, 256, 256).to(device)
        ms_img = torch.randn(batch_size, 5, 256, 256).to(device)
        
        print(f"✓ 测试数据创建成功")
        print(f"  - SAR图像形状: {sar_img.shape}")
        print(f"  - MS图像形状: {ms_img.shape}")
        print(f"  - 设备: {device}")
        
        # 测试不同模式的前向传播
        model.eval()
        
        # 测试SAR重建
        with torch.no_grad():
            sar_results = model(sar_img, ms_img, mode='sar_recon')
            print(f"✓ SAR重建模式测试成功")
            print(f"  - 重建损失: {sar_results['loss'].item():.4f}")
            print(f"  - 预测图像形状: {sar_results['pred'].shape}")
            print(f"  - 掩码形状: {sar_results['mask'].shape}")
        
        # 测试MS重建
        with torch.no_grad():
            ms_results = model(sar_img, ms_img, mode='ms_recon')
            print(f"✓ MS重建模式测试成功")
            print(f"  - 重建损失: {ms_results['loss'].item():.4f}")
            print(f"  - 预测图像形状: {ms_results['pred'].shape}")
            print(f"  - 掩码形状: {ms_results['mask'].shape}")
        
        # 测试双模态重建
        with torch.no_grad():
            both_results = model(sar_img, ms_img, mode='both')
            print(f"✓ 双模态重建模式测试成功")
            print(f"  - SAR损失: {both_results['sar_loss'].item():.4f}")
            print(f"  - MS损失: {both_results['ms_loss'].item():.4f}")
            print(f"  - 总损失: {both_results['total_loss'].item():.4f}")
        
        # 测试特征提取
        with torch.no_grad():
            features = model.extract_features(sar_img, ms_img)
            print(f"✓ 特征提取测试成功")
            print(f"  - 特征形状: {features.shape}")
        
        return True, both_results
    except Exception as e:
        print(f"✗ 前向传播测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, None

def test_loss_function(model_output):
    """测试损失函数"""
    print("\n" + "=" * 50)
    print("测试3: 损失函数")
    print("=" * 50)
    
    try:
        # 测试MAE损失函数
        criterion = FusionMAELoss(
            lambda_sar=1.0,
            lambda_ms=1.0,
            loss_type='mse',
            norm_pix_loss=True
        )
        
        loss = criterion(model_output)
        print(f"✓ MAE损失函数测试成功")
        print(f"  - 计算损失: {loss.item():.4f}")
        print(f"  - 损失类型: {type(loss).__name__}")
        
        # 测试不同损失类型
        for loss_type in ['l1', 'smooth_l1']:
            criterion_alt = FusionMAELoss(loss_type=loss_type)
            loss_alt = criterion_alt(model_output)
            print(f"✓ {loss_type}损失测试成功: {loss_alt.item():.4f}")
        
        return True
    except Exception as e:
        print(f"✗ 损失函数测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_masking_mechanism(model):
    """测试掩码机制"""
    print("\n" + "=" * 50)
    print("测试4: 掩码机制")
    print("=" * 50)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    
    try:
        # 创建测试数据
        sar_img = torch.randn(1, 1, 256, 256).to(device)
        ms_img = torch.randn(1, 5, 256, 256).to(device)
        
        # 测试不同掩码比例
        mask_ratios = [0.5, 0.75, 0.9]
        
        for mask_ratio in mask_ratios:
            model.mask_ratio = mask_ratio
            
            with torch.no_grad():
                results = model(sar_img, ms_img, mode='sar_recon')
                mask = results['mask']
                
                # 计算实际掩码比例
                actual_mask_ratio = mask.float().mean().item()
                
                print(f"✓ 掩码比例 {mask_ratio} 测试成功")
                print(f"  - 期望掩码比例: {mask_ratio:.2f}")
                print(f"  - 实际掩码比例: {actual_mask_ratio:.2f}")
                print(f"  - 掩码patches数量: {mask.sum().item()}")
                print(f"  - 总patches数量: {mask.numel()}")
        
        return True
    except Exception as e:
        print(f"✗ 掩码机制测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_gradient_flow(model):
    """测试梯度流"""
    print("\n" + "=" * 50)
    print("测试5: 梯度流")
    print("=" * 50)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    model.train()
    
    try:
        # 创建测试数据
        sar_img = torch.randn(2, 1, 256, 256).to(device)
        ms_img = torch.randn(2, 5, 256, 256).to(device)
        
        # 前向传播
        results = model(sar_img, ms_img, mode='both')
        loss = results['total_loss']
        
        # 反向传播
        loss.backward()
        
        # 检查梯度
        grad_norms = []
        zero_grad_count = 0
        total_params = 0
        
        for name, param in model.named_parameters():
            if param.grad is not None:
                grad_norm = param.grad.norm().item()
                grad_norms.append(grad_norm)
                if grad_norm == 0:
                    zero_grad_count += 1
            total_params += 1
        
        print(f"✓ 梯度流测试成功")
        print(f"  - 总参数数量: {total_params}")
        print(f"  - 有梯度的参数: {len(grad_norms)}")
        print(f"  - 零梯度参数: {zero_grad_count}")
        print(f"  - 平均梯度范数: {np.mean(grad_norms):.6f}")
        print(f"  - 最大梯度范数: {np.max(grad_norms):.6f}")
        print(f"  - 最小梯度范数: {np.min(grad_norms):.6f}")
        
        return True
    except Exception as e:
        print(f"✗ 梯度流测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def visualize_reconstruction(model, save_dir='./test_results'):
    """可视化重建结果"""
    print("\n" + "=" * 50)
    print("测试6: 重建可视化")
    print("=" * 50)
    
    os.makedirs(save_dir, exist_ok=True)
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    model.eval()
    
    try:
        # 创建测试数据
        sar_img = torch.randn(1, 1, 256, 256).to(device)
        ms_img = torch.randn(1, 5, 256, 256).to(device)
        
        with torch.no_grad():
            # SAR重建
            sar_results = model(sar_img, ms_img, mode='sar_recon')
            
            # MS重建
            ms_results = model(sar_img, ms_img, mode='ms_recon')
        
        # 转换为numpy用于可视化
        sar_orig = sar_img[0, 0].cpu().numpy()
        sar_recon = sar_results['pred'][0, 0].cpu().numpy()
        sar_mask = sar_results['mask'][0].cpu().numpy().reshape(16, 16)
        
        ms_orig = ms_img[0, 0].cpu().numpy()  # 只显示第一个通道
        ms_recon = ms_results['pred'][0, 0].cpu().numpy()
        ms_mask = ms_results['mask'][0].cpu().numpy().reshape(16, 16)
        
        # 创建可视化
        fig, axes = plt.subplots(2, 4, figsize=(16, 8))
        
        # SAR行
        axes[0, 0].imshow(sar_orig, cmap='gray')
        axes[0, 0].set_title('SAR原图')
        axes[0, 0].axis('off')
        
        axes[0, 1].imshow(sar_recon, cmap='gray')
        axes[0, 1].set_title('SAR重建')
        axes[0, 1].axis('off')
        
        axes[0, 2].imshow(sar_mask, cmap='Reds')
        axes[0, 2].set_title('SAR掩码')
        axes[0, 2].axis('off')
        
        axes[0, 3].imshow(np.abs(sar_orig - sar_recon), cmap='hot')
        axes[0, 3].set_title('SAR差异')
        axes[0, 3].axis('off')
        
        # MS行
        axes[1, 0].imshow(ms_orig, cmap='viridis')
        axes[1, 0].set_title('MS原图(Ch1)')
        axes[1, 0].axis('off')
        
        axes[1, 1].imshow(ms_recon, cmap='viridis')
        axes[1, 1].set_title('MS重建(Ch1)')
        axes[1, 1].axis('off')
        
        axes[1, 2].imshow(ms_mask, cmap='Reds')
        axes[1, 2].set_title('MS掩码')
        axes[1, 2].axis('off')
        
        axes[1, 3].imshow(np.abs(ms_orig - ms_recon), cmap='hot')
        axes[1, 3].set_title('MS差异')
        axes[1, 3].axis('off')
        
        plt.tight_layout()
        
        # 保存图像
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        save_path = os.path.join(save_dir, f'reconstruction_test_{timestamp}.png')
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"✓ 重建可视化测试成功")
        print(f"  - 可视化结果已保存到: {save_path}")
        
        return True
    except Exception as e:
        print(f"✗ 重建可视化测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def run_all_tests():
    """运行所有测试"""
    print("开始Fusion-MAE功能测试...")
    print("测试时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    test_results = []
    
    # 测试1: 模型创建
    success, model = test_model_creation()
    test_results.append(("模型创建", success))
    
    if not success:
        print("\n模型创建失败，终止测试")
        return
    
    # 测试2: 前向传播
    success, model_output = test_forward_pass(model)
    test_results.append(("前向传播", success))
    
    if not success:
        print("\n前向传播失败，跳过后续测试")
        return
    
    # 测试3: 损失函数
    success = test_loss_function(model_output)
    test_results.append(("损失函数", success))
    
    # 测试4: 掩码机制
    success = test_masking_mechanism(model)
    test_results.append(("掩码机制", success))
    
    # 测试5: 梯度流
    success = test_gradient_flow(model)
    test_results.append(("梯度流", success))
    
    # 测试6: 重建可视化
    success = visualize_reconstruction(model)
    test_results.append(("重建可视化", success))
    
    # 打印测试总结
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    
    passed = 0
    for test_name, success in test_results:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"{test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n总测试数: {len(test_results)}")
    print(f"通过测试: {passed}")
    print(f"失败测试: {len(test_results) - passed}")
    print(f"通过率: {passed/len(test_results)*100:.1f}%")
    
    if passed == len(test_results):
        print("\n🎉 所有测试通过！Fusion-MAE模型功能正常。")
    else:
        print(f"\n⚠️  有 {len(test_results) - passed} 个测试失败，请检查相关功能。")

if __name__ == "__main__":
    run_all_tests()
