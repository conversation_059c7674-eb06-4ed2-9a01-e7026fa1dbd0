# 更新日志

## [v2.0.0] - 2024-07-09

### 🎉 重大更新：Fusion-MAE实现

#### 新增功能
- **Fusion-MAE核心模型** (`fusion_mae.py`)
  - 实现基于ViT的跨模态掩码自编码器
  - 支持SAR和多光谱影像的双向重建
  - 75%高掩码率的自监督学习机制
  - 轻量级Transformer解码器设计

- **专用训练脚本** (`train_fusion_mae.py`)
  - 完整的MAE训练流程
  - 学习率预热和余弦退火调度
  - 双模态重建损失计算
  - 训练过程可视化和监控

- **综合测试套件** (`test_fusion_mae.py`)
  - 6项全面功能测试
  - 掩码机制验证
  - 梯度流检查
  - 重建结果可视化

#### 数据处理增强
- **FusionMAEDataset类** (`utils/dataset.py`)
  - 专为MAE设计的数据加载器
  - 自动数据归一化和统计计算
  - 支持数据增强 (翻转、旋转)
  - 灵活的图像尺寸调整

#### 损失函数扩展
- **FusionMAELoss类** (`utils/loss.py`)
  - 掩码区域重建损失
  - 支持多种损失类型 (MSE, L1, Smooth L1)
  - 像素级归一化选项
  - 双模态损失权重配置

- **CombinedLoss类** (`utils/loss.py`)
  - 结合MAE重建损失和传统融合损失
  - 用于微调阶段的混合训练

#### 技术特性
- **模态类型嵌入**: 区分SAR和MS模态的可学习参数
- **位置编码**: 保持空间位置信息
- **自适应掩码**: 支持不同掩码比例配置
- **特征提取接口**: 便于下游任务使用

### 🔧 技术改进
- **内存优化**: 高效的patch处理和掩码机制
- **训练稳定性**: 梯度裁剪和权重初始化
- **代码结构**: 模块化设计，易于扩展
- **错误处理**: 完善的异常处理和日志记录

### 📊 性能表现
- **模型参数**: ~138M参数
- **测试通过率**: 100% (6/6项测试)
- **掩码精度**: 与设定比例完全一致
- **梯度流**: 正常，无异常情况

### 📚 文档完善
- **README_Fusion_MAE.md**: 详细使用说明
- **代码注释**: 中文注释，便于理解
- **参数说明**: 完整的配置参数文档
- **使用示例**: 多种使用场景演示

### 🧪 测试验证
- **模型创建测试**: 验证模型正确初始化
- **前向传播测试**: 确保各模式正常工作
- **损失函数测试**: 验证损失计算正确性
- **掩码机制测试**: 确保掩码比例准确
- **梯度流测试**: 检查反向传播正常
- **可视化测试**: 生成重建结果图像

### 🔄 兼容性
- **向后兼容**: 保留原有SRCNN功能
- **模块独立**: 新功能不影响原有代码
- **灵活配置**: 支持多种参数组合

---

## [v1.0.0] - 原始版本

### 基础功能
- **SRCNN模型实现** (`model.py`)
  - 基于残差网络的超分辨率重建
  - 注意力机制集成
  - 多尺度特征提取

- **训练框架** (`train.py`)
  - 完整的训练流程
  - 损失函数集成
  - 模型保存和加载

- **数据处理** (`utils/dataset.py`)
  - MedicalImageDataset类
  - TIFF格式图像读取
  - 基础数据预处理

- **损失函数** (`utils/loss.py`)
  - FusionLoss类
  - 多种损失组合
  - VGG感知损失

### 支持功能
- CUDA加速支持
- 多种学习率调度策略
- 训练过程监控
- 模型性能评估
