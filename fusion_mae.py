import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import numpy as np
from typing import Tuple, Optional


class PatchEmbedding(nn.Module):
    """
    将图像分割成patches并进行线性投射
    """
    def __init__(self, img_size=256, patch_size=16, in_channels=3, embed_dim=768):
        super().__init__()
        self.img_size = img_size
        self.patch_size = patch_size
        self.num_patches = (img_size // patch_size) ** 2
        
        # 使用卷积层实现patch embedding
        self.projection = nn.Conv2d(
            in_channels, embed_dim, 
            kernel_size=patch_size, stride=patch_size
        )
        
    def forward(self, x):
        # x: (B, C, H, W) -> (B, embed_dim, H//patch_size, W//patch_size)
        x = self.projection(x)
        # 展平为序列: (B, embed_dim, num_patches) -> (B, num_patches, embed_dim)
        x = x.flatten(2).transpose(1, 2)
        return x


class MultiHeadAttention(nn.Module):
    """
    多头自注意力机制
    """
    def __init__(self, embed_dim=768, num_heads=12, dropout=0.1):
        super().__init__()
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads
        
        assert self.head_dim * num_heads == embed_dim, "embed_dim必须能被num_heads整除"
        
        self.qkv = nn.Linear(embed_dim, embed_dim * 3)
        self.proj = nn.Linear(embed_dim, embed_dim)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x):
        B, N, C = x.shape
        
        # 生成Q, K, V
        qkv = self.qkv(x).reshape(B, N, 3, self.num_heads, self.head_dim).permute(2, 0, 3, 1, 4)
        q, k, v = qkv[0], qkv[1], qkv[2]
        
        # 计算注意力分数
        attn = (q @ k.transpose(-2, -1)) * (self.head_dim ** -0.5)
        attn = F.softmax(attn, dim=-1)
        attn = self.dropout(attn)
        
        # 应用注意力权重
        x = (attn @ v).transpose(1, 2).reshape(B, N, C)
        x = self.proj(x)
        x = self.dropout(x)
        
        return x


class MLP(nn.Module):
    """
    前馈神经网络
    """
    def __init__(self, embed_dim=768, mlp_ratio=4.0, dropout=0.1):
        super().__init__()
        hidden_dim = int(embed_dim * mlp_ratio)
        self.fc1 = nn.Linear(embed_dim, hidden_dim)
        self.fc2 = nn.Linear(hidden_dim, embed_dim)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x):
        x = self.fc1(x)
        x = F.gelu(x)
        x = self.dropout(x)
        x = self.fc2(x)
        x = self.dropout(x)
        return x


class TransformerBlock(nn.Module):
    """
    Transformer编码器块
    """
    def __init__(self, embed_dim=768, num_heads=12, mlp_ratio=4.0, dropout=0.1):
        super().__init__()
        self.norm1 = nn.LayerNorm(embed_dim)
        self.attn = MultiHeadAttention(embed_dim, num_heads, dropout)
        self.norm2 = nn.LayerNorm(embed_dim)
        self.mlp = MLP(embed_dim, mlp_ratio, dropout)
        
    def forward(self, x):
        # 残差连接 + 层归一化
        x = x + self.attn(self.norm1(x))
        x = x + self.mlp(self.norm2(x))
        return x


class CrossModalMaskingEncoder(nn.Module):
    """
    跨模态掩码编码器 - Fusion-MAE的核心
    """
    def __init__(
        self, 
        img_size=256, 
        patch_size=16, 
        sar_channels=1, 
        ms_channels=5,
        embed_dim=768, 
        depth=12, 
        num_heads=12, 
        mlp_ratio=4.0, 
        dropout=0.1
    ):
        super().__init__()
        self.img_size = img_size
        self.patch_size = patch_size
        self.num_patches = (img_size // patch_size) ** 2
        
        # SAR和MS的patch embedding
        self.sar_patch_embed = PatchEmbedding(img_size, patch_size, sar_channels, embed_dim)
        self.ms_patch_embed = PatchEmbedding(img_size, patch_size, ms_channels, embed_dim)
        
        # 位置编码 (可学习)
        self.pos_embed = nn.Parameter(torch.zeros(1, self.num_patches, embed_dim))
        
        # 模态类型嵌入 (区分SAR和MS)
        self.sar_type_embed = nn.Parameter(torch.zeros(1, 1, embed_dim))
        self.ms_type_embed = nn.Parameter(torch.zeros(1, 1, embed_dim))
        
        # Transformer编码器层
        self.blocks = nn.ModuleList([
            TransformerBlock(embed_dim, num_heads, mlp_ratio, dropout)
            for _ in range(depth)
        ])
        
        self.norm = nn.LayerNorm(embed_dim)
        self.dropout = nn.Dropout(dropout)
        
        # 初始化权重
        self._init_weights()
        
    def _init_weights(self):
        # 初始化位置编码
        torch.nn.init.trunc_normal_(self.pos_embed, std=0.02)
        torch.nn.init.trunc_normal_(self.sar_type_embed, std=0.02)
        torch.nn.init.trunc_normal_(self.ms_type_embed, std=0.02)
        
        # 初始化其他参数
        for m in self.modules():
            if isinstance(m, nn.Linear):
                torch.nn.init.trunc_normal_(m.weight, std=0.02)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.LayerNorm):
                nn.init.constant_(m.bias, 0)
                nn.init.constant_(m.weight, 1.0)
    
    def random_masking(self, x, mask_ratio=0.75):
        """
        随机掩码策略
        Args:
            x: 输入序列 [B, N, D]
            mask_ratio: 掩码比例
        Returns:
            x_masked: 掩码后的可见tokens
            mask: 掩码标记 [B, N] (0表示保留, 1表示掩码)
            ids_restore: 用于恢复原始顺序的索引
        """
        B, N, D = x.shape
        len_keep = int(N * (1 - mask_ratio))
        
        # 生成随机噪声并排序
        noise = torch.rand(B, N, device=x.device)
        ids_shuffle = torch.argsort(noise, dim=1)
        ids_restore = torch.argsort(ids_shuffle, dim=1)
        
        # 保留前len_keep个tokens
        ids_keep = ids_shuffle[:, :len_keep]
        x_masked = torch.gather(x, dim=1, index=ids_keep.unsqueeze(-1).repeat(1, 1, D))
        
        # 生成掩码
        mask = torch.ones([B, N], device=x.device)
        mask[:, :len_keep] = 0
        mask = torch.gather(mask, dim=1, index=ids_restore)
        
        return x_masked, mask, ids_restore
    
    def forward(self, sar_img, ms_img, mask_ratio=0.75, mode='sar_recon'):
        """
        前向传播
        Args:
            sar_img: SAR图像 [B, sar_channels, H, W]
            ms_img: MS图像 [B, ms_channels, H, W]
            mask_ratio: 掩码比例
            mode: 'sar_recon' 或 'ms_recon'，决定掩码哪个模态
        """
        B = sar_img.shape[0]
        
        # Patch embedding
        sar_patches = self.sar_patch_embed(sar_img)  # [B, N, D]
        ms_patches = self.ms_patch_embed(ms_img)     # [B, N, D]
        
        # 添加位置编码和模态类型编码
        sar_patches = sar_patches + self.pos_embed + self.sar_type_embed
        ms_patches = ms_patches + self.pos_embed + self.ms_type_embed
        
        if mode == 'sar_recon':
            # 重建SAR: 掩码SAR，保留完整MS
            sar_masked, mask, ids_restore = self.random_masking(sar_patches, mask_ratio)
            # 拼接可见SAR patches和完整MS patches
            x = torch.cat([sar_masked, ms_patches], dim=1)  # [B, len_keep + N, D]
        else:
            # 重建MS: 掩码MS，保留完整SAR
            ms_masked, mask, ids_restore = self.random_masking(ms_patches, mask_ratio)
            # 拼接完整SAR patches和可见MS patches
            x = torch.cat([sar_patches, ms_masked], dim=1)  # [B, N + len_keep, D]
        
        # 通过Transformer编码器
        x = self.dropout(x)
        for block in self.blocks:
            x = block(x)
        x = self.norm(x)
        
        return x, mask, ids_restore


class LightweightDecoder(nn.Module):
    """
    轻量级解码器 - 用于重建被掩码的图像块
    """
    def __init__(
        self, 
        num_patches, 
        patch_size=16, 
        encoder_embed_dim=768, 
        decoder_embed_dim=512, 
        decoder_depth=8, 
        decoder_num_heads=16, 
        mlp_ratio=4.0,
        out_channels=1  # SAR=1, MS=5
    ):
        super().__init__()
        self.num_patches = num_patches
        self.patch_size = patch_size
        self.out_channels = out_channels
        
        # 编码器到解码器的投射
        self.decoder_embed = nn.Linear(encoder_embed_dim, decoder_embed_dim)
        
        # 掩码token (可学习参数)
        self.mask_token = nn.Parameter(torch.zeros(1, 1, decoder_embed_dim))
        
        # 解码器位置编码
        self.decoder_pos_embed = nn.Parameter(torch.zeros(1, num_patches, decoder_embed_dim))
        
        # 解码器Transformer块
        self.decoder_blocks = nn.ModuleList([
            TransformerBlock(decoder_embed_dim, decoder_num_heads, mlp_ratio)
            for _ in range(decoder_depth)
        ])
        
        self.decoder_norm = nn.LayerNorm(decoder_embed_dim)
        
        # 重建头：将特征映射回像素值
        self.decoder_pred = nn.Linear(
            decoder_embed_dim, 
            patch_size**2 * out_channels
        )
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        torch.nn.init.trunc_normal_(self.mask_token, std=0.02)
        torch.nn.init.trunc_normal_(self.decoder_pos_embed, std=0.02)
        
        for m in self.modules():
            if isinstance(m, nn.Linear):
                torch.nn.init.trunc_normal_(m.weight, std=0.02)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.LayerNorm):
                nn.init.constant_(m.bias, 0)
                nn.init.constant_(m.weight, 1.0)
    
    def forward(self, x, ids_restore, mode='sar_recon'):
        """
        解码器前向传播
        Args:
            x: 编码器输出 [B, len_visible, encoder_embed_dim]
            ids_restore: 恢复原始顺序的索引
            mode: 'sar_recon' 或 'ms_recon'
        """
        # 投射到解码器维度
        x = self.decoder_embed(x)
        
        if mode == 'sar_recon':
            # 重建SAR: x包含[可见SAR, 完整MS]
            # 我们只需要重建被掩码的SAR部分
            B, total_len, D = x.shape
            len_visible_sar = total_len - self.num_patches  # 可见SAR patches数量
            
            # 分离可见SAR和完整MS
            visible_sar = x[:, :len_visible_sar, :]  # [B, len_visible_sar, D]
            
            # 为被掩码的SAR位置添加mask tokens
            mask_tokens = self.mask_token.repeat(B, self.num_patches - len_visible_sar, 1)
            
            # 重新组合：[可见SAR, 掩码SAR]
            x_full = torch.cat([visible_sar, mask_tokens], dim=1)  # [B, num_patches, D]
            
        else:
            # 重建MS: x包含[完整SAR, 可见MS]
            B, total_len, D = x.shape
            len_visible_ms = total_len - self.num_patches  # 可见MS patches数量
            
            # 分离完整SAR和可见MS
            visible_ms = x[:, self.num_patches:, :]  # [B, len_visible_ms, D]
            
            # 为被掩码的MS位置添加mask tokens
            mask_tokens = self.mask_token.repeat(B, self.num_patches - len_visible_ms, 1)
            
            # 重新组合：[可见MS, 掩码MS]
            x_full = torch.cat([visible_ms, mask_tokens], dim=1)  # [B, num_patches, D]
        
        # 恢复原始patch顺序
        x_full = torch.gather(x_full, dim=1, index=ids_restore.unsqueeze(-1).repeat(1, 1, D))
        
        # 添加位置编码
        x_full = x_full + self.decoder_pos_embed
        
        # 通过解码器Transformer块
        for block in self.decoder_blocks:
            x_full = block(x_full)
        x_full = self.decoder_norm(x_full)
        
        # 预测像素值
        x_full = self.decoder_pred(x_full)  # [B, num_patches, patch_size^2 * out_channels]
        
        return x_full


class FusionMAE(nn.Module):
    """
    跨模态生成式掩码自编码器 (Fusion-MAE)
    核心思想：通过"完形填空"任务强迫模型深度理解并融合SAR和MS两种模态的信息
    """
    def __init__(
        self,
        img_size=256,
        patch_size=16,
        sar_channels=1,
        ms_channels=5,
        encoder_embed_dim=768,
        encoder_depth=12,
        encoder_num_heads=12,
        decoder_embed_dim=512,
        decoder_depth=8,
        decoder_num_heads=16,
        mlp_ratio=4.0,
        dropout=0.1,
        mask_ratio=0.75
    ):
        super().__init__()
        self.img_size = img_size
        self.patch_size = patch_size
        self.num_patches = (img_size // patch_size) ** 2
        self.mask_ratio = mask_ratio

        # 跨模态掩码编码器
        self.encoder = CrossModalMaskingEncoder(
            img_size=img_size,
            patch_size=patch_size,
            sar_channels=sar_channels,
            ms_channels=ms_channels,
            embed_dim=encoder_embed_dim,
            depth=encoder_depth,
            num_heads=encoder_num_heads,
            mlp_ratio=mlp_ratio,
            dropout=dropout
        )

        # SAR重建解码器
        self.sar_decoder = LightweightDecoder(
            num_patches=self.num_patches,
            patch_size=patch_size,
            encoder_embed_dim=encoder_embed_dim,
            decoder_embed_dim=decoder_embed_dim,
            decoder_depth=decoder_depth,
            decoder_num_heads=decoder_num_heads,
            mlp_ratio=mlp_ratio,
            out_channels=sar_channels
        )

        # MS重建解码器
        self.ms_decoder = LightweightDecoder(
            num_patches=self.num_patches,
            patch_size=patch_size,
            encoder_embed_dim=encoder_embed_dim,
            decoder_embed_dim=decoder_embed_dim,
            decoder_depth=decoder_depth,
            decoder_num_heads=decoder_num_heads,
            mlp_ratio=mlp_ratio,
            out_channels=ms_channels
        )

    def patchify(self, imgs, channels):
        """
        将图像转换为patches
        Args:
            imgs: [B, C, H, W]
            channels: 通道数
        Returns:
            patches: [B, num_patches, patch_size^2 * channels]
        """
        p = self.patch_size
        assert imgs.shape[2] == imgs.shape[3] and imgs.shape[2] % p == 0

        h = w = imgs.shape[2] // p
        x = imgs.reshape(imgs.shape[0], channels, h, p, w, p)
        x = torch.einsum('nchpwq->nhwpqc', x)
        x = x.reshape(imgs.shape[0], h * w, p**2 * channels)
        return x

    def unpatchify(self, x, channels):
        """
        将patches转换回图像
        Args:
            x: [B, num_patches, patch_size^2 * channels]
            channels: 通道数
        Returns:
            imgs: [B, C, H, W]
        """
        p = self.patch_size
        h = w = int(x.shape[1]**.5)
        assert h * w == x.shape[1]

        x = x.reshape(x.shape[0], h, w, p, p, channels)
        x = torch.einsum('nhwpqc->nchpwq', x)
        imgs = x.reshape(x.shape[0], channels, h * p, w * p)
        return imgs

    def forward_loss(self, imgs, pred, mask, channels):
        """
        计算重建损失 (仅在被掩码的patches上)
        Args:
            imgs: 原始图像 [B, C, H, W]
            pred: 预测的patches [B, num_patches, patch_size^2 * channels]
            mask: 掩码 [B, num_patches] (1表示被掩码)
            channels: 通道数
        """
        target = self.patchify(imgs, channels)  # [B, num_patches, patch_size^2 * channels]

        # 计算MSE损失
        loss = (pred - target) ** 2
        loss = loss.mean(dim=-1)  # [B, num_patches] 每个patch的平均损失

        # 只计算被掩码区域的损失
        loss = (loss * mask).sum() / mask.sum()  # 平均损失
        return loss

    def forward(self, sar_img, ms_img, mode='both'):
        """
        前向传播
        Args:
            sar_img: SAR图像 [B, sar_channels, H, W]
            ms_img: MS图像 [B, ms_channels, H, W]
            mode: 'sar_recon', 'ms_recon', 或 'both'
        Returns:
            根据mode返回不同的结果
        """
        if mode == 'sar_recon':
            return self._forward_single_recon(sar_img, ms_img, 'sar_recon')
        elif mode == 'ms_recon':
            return self._forward_single_recon(sar_img, ms_img, 'ms_recon')
        elif mode == 'both':
            # 同时进行SAR和MS重建
            sar_results = self._forward_single_recon(sar_img, ms_img, 'sar_recon')
            ms_results = self._forward_single_recon(sar_img, ms_img, 'ms_recon')
            return {
                'sar_loss': sar_results['loss'],
                'ms_loss': ms_results['loss'],
                'total_loss': sar_results['loss'] + ms_results['loss'],
                'sar_pred': sar_results['pred'],
                'ms_pred': ms_results['pred'],
                'sar_mask': sar_results['mask'],
                'ms_mask': ms_results['mask']
            }
        else:
            raise ValueError(f"不支持的模式: {mode}")

    def _forward_single_recon(self, sar_img, ms_img, mode):
        """
        单一重建任务的前向传播
        """
        # 编码器前向传播
        latent, mask, ids_restore = self.encoder(
            sar_img, ms_img, self.mask_ratio, mode
        )

        # 选择对应的解码器
        if mode == 'sar_recon':
            pred = self.sar_decoder(latent, ids_restore, mode)
            loss = self.forward_loss(sar_img, pred, mask, self.encoder.sar_patch_embed.projection.in_channels)
            pred_img = self.unpatchify(pred, self.encoder.sar_patch_embed.projection.in_channels)
        else:  # ms_recon
            pred = self.ms_decoder(latent, ids_restore, mode)
            loss = self.forward_loss(ms_img, pred, mask, self.encoder.ms_patch_embed.projection.in_channels)
            pred_img = self.unpatchify(pred, self.encoder.ms_patch_embed.projection.in_channels)

        return {
            'loss': loss,
            'pred': pred_img,
            'mask': mask
        }

    def extract_features(self, sar_img, ms_img):
        """
        提取深度特征用于下游任务 (推理阶段)
        这是训练好的编码器的主要用途
        """
        with torch.no_grad():
            # 不使用掩码，提取完整特征
            # Patch embedding
            sar_patches = self.encoder.sar_patch_embed(sar_img)
            ms_patches = self.encoder.ms_patch_embed(ms_img)

            # 添加位置编码和模态类型编码
            sar_patches = sar_patches + self.encoder.pos_embed + self.encoder.sar_type_embed
            ms_patches = ms_patches + self.encoder.pos_embed + self.encoder.ms_type_embed

            # 拼接两个模态的完整特征
            x = torch.cat([sar_patches, ms_patches], dim=1)  # [B, 2*num_patches, embed_dim]

            # 通过编码器
            for block in self.encoder.blocks:
                x = block(x)
            x = self.encoder.norm(x)

            return x  # [B, 2*num_patches, embed_dim]


def create_fusion_mae_model(
    img_size=256,
    patch_size=16,
    sar_channels=1,
    ms_channels=5,
    **kwargs
):
    """
    创建Fusion-MAE模型的工厂函数
    """
    model = FusionMAE(
        img_size=img_size,
        patch_size=patch_size,
        sar_channels=sar_channels,
        ms_channels=ms_channels,
        **kwargs
    )
    return model

