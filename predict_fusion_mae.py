import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
import os
import argparse
import rasterio
from datetime import datetime
import logging

# 导入我们的模块
from fusion_mae import create_fusion_mae_model
from utils.dataset import FusionMAEDataset

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Fusion-MAE影像预测脚本')
    
    # 输入文件
    parser.add_argument('--sar_path', type=str, required=True, 
                       help='SAR影像路径')
    parser.add_argument('--ms_path', type=str, required=True, 
                       help='多光谱影像路径')
    
    # 模型参数
    parser.add_argument('--model_path', type=str, required=True, 
                       help='训练好的模型权重路径')
    parser.add_argument('--img_size', type=int, default=256, 
                       help='输入图像大小')
    parser.add_argument('--patch_size', type=int, default=16, 
                       help='patch大小')
    parser.add_argument('--sar_channels', type=int, default=1, 
                       help='SAR图像通道数')
    parser.add_argument('--ms_channels', type=int, default=5, 
                       help='多光谱图像通道数')
    
    # 输出设置
    parser.add_argument('--output_dir', type=str, default='./predictions', 
                       help='预测结果保存目录')
    parser.add_argument('--save_features', action='store_true', 
                       help='是否保存提取的特征')
    parser.add_argument('--visualize', action='store_true', 
                       help='是否生成可视化结果')
    
    # 其他参数
    parser.add_argument('--cuda', action='store_true', default=True, 
                       help='使用CUDA')
    parser.add_argument('--batch_size', type=int, default=1, 
                       help='批次大小')
    
    return parser.parse_args()

def load_image(image_path, target_size=None):
    """
    加载TIFF格式的遥感影像
    """
    try:
        with rasterio.open(image_path) as src:
            if src.count > 1:
                # 多波段: (C, H, W) -> (H, W, C)
                image = np.transpose(src.read(), (1, 2, 0))
            else:
                # 单波段: (H, W) -> (H, W, 1)
                image = src.read(1)
                image = np.expand_dims(image, axis=-1)
            
            # 转换为tensor: (H, W, C) -> (C, H, W)
            tensor = torch.from_numpy(image).permute(2, 0, 1).float()
            
            # 调整大小
            if target_size and tensor.shape[-2:] != (target_size, target_size):
                tensor = torch.nn.functional.interpolate(
                    tensor.unsqueeze(0), 
                    size=(target_size, target_size), 
                    mode='bilinear', 
                    align_corners=False
                ).squeeze(0)
            
            logging.info(f"成功加载影像: {image_path}, 形状: {tensor.shape}")
            return tensor
            
    except Exception as e:
        logging.error(f"加载影像失败 {image_path}: {str(e)}")
        return None

def load_model(model_path, device, **model_kwargs):
    """
    加载训练好的模型
    """
    try:
        # 创建模型
        model = create_fusion_mae_model(**model_kwargs)
        
        # 加载权重
        checkpoint = torch.load(model_path, map_location=device)
        
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
            logging.info(f"从检查点加载模型权重: epoch {checkpoint.get('epoch', 'unknown')}")
        else:
            model.load_state_dict(checkpoint)
            logging.info("直接加载模型权重")
        
        model = model.to(device)
        model.eval()
        
        logging.info(f"模型加载成功，参数数量: {sum(p.numel() for p in model.parameters()):,}")
        return model
        
    except Exception as e:
        logging.error(f"模型加载失败: {str(e)}")
        return None

def predict_fusion(model, sar_img, ms_img, device):
    """
    进行影像融合预测
    """
    model.eval()
    
    with torch.no_grad():
        # 确保输入在正确的设备上
        sar_img = sar_img.to(device)
        ms_img = ms_img.to(device)
        
        # 添加批次维度
        if sar_img.dim() == 3:
            sar_img = sar_img.unsqueeze(0)
        if ms_img.dim() == 3:
            ms_img = ms_img.unsqueeze(0)
        
        # 提取深度特征用于融合
        features = model.extract_features(sar_img, ms_img)
        
        # 这里可以添加特定的融合策略
        # 目前我们使用简单的特征融合方法
        
        # 分离SAR和MS特征
        num_patches = features.shape[1] // 2
        sar_features = features[:, :num_patches, :]  # 前一半是SAR特征
        ms_features = features[:, num_patches:, :]   # 后一半是MS特征
        
        # 简单的特征融合：加权平均
        fused_features = 0.5 * sar_features + 0.5 * ms_features
        
        # 这里需要一个解码器将特征转换回图像
        # 暂时返回上采样的MS图像作为融合结果
        fusion_result = torch.nn.functional.interpolate(
            ms_img, 
            size=sar_img.shape[-2:], 
            mode='bilinear', 
            align_corners=False
        )
        
        return {
            'fusion_result': fusion_result.squeeze(0),  # 移除批次维度
            'sar_features': sar_features.squeeze(0),
            'ms_features': ms_features.squeeze(0),
            'fused_features': fused_features.squeeze(0)
        }

def save_image(tensor, save_path, original_path=None):
    """
    保存预测结果为TIFF格式
    """
    try:
        # 转换tensor格式: (C, H, W) -> (C, H, W)
        if tensor.dim() == 3:
            image_array = tensor.cpu().numpy()
        else:
            image_array = tensor.cpu().numpy()
        
        # 获取原始影像的地理信息（如果提供）
        profile = {
            'driver': 'GTiff',
            'height': image_array.shape[1],
            'width': image_array.shape[2],
            'count': image_array.shape[0],
            'dtype': image_array.dtype,
            'compress': 'lzw'
        }
        
        if original_path:
            try:
                with rasterio.open(original_path) as src:
                    profile.update({
                        'crs': src.crs,
                        'transform': src.transform
                    })
            except:
                logging.warning(f"无法读取原始影像的地理信息: {original_path}")
        
        # 保存影像
        with rasterio.open(save_path, 'w', **profile) as dst:
            dst.write(image_array)
        
        logging.info(f"预测结果已保存: {save_path}")
        
    except Exception as e:
        logging.error(f"保存影像失败: {str(e)}")

def visualize_results(sar_img, ms_img, fusion_result, save_path):
    """
    可视化预测结果
    """
    try:
        fig, axes = plt.subplots(1, 4, figsize=(20, 5))
        
        # SAR影像
        sar_display = sar_img[0].cpu().numpy() if sar_img.shape[0] == 1 else sar_img[0].cpu().numpy()
        axes[0].imshow(sar_display, cmap='gray')
        axes[0].set_title('SAR影像')
        axes[0].axis('off')
        
        # MS影像（显示前3个波段作为RGB）
        if ms_img.shape[0] >= 3:
            ms_rgb = ms_img[:3].cpu().numpy().transpose(1, 2, 0)
            # 归一化到0-1
            ms_rgb = (ms_rgb - ms_rgb.min()) / (ms_rgb.max() - ms_rgb.min())
            axes[1].imshow(ms_rgb)
        else:
            axes[1].imshow(ms_img[0].cpu().numpy(), cmap='viridis')
        axes[1].set_title('多光谱影像')
        axes[1].axis('off')
        
        # 融合结果
        if fusion_result.shape[0] >= 3:
            fusion_rgb = fusion_result[:3].cpu().numpy().transpose(1, 2, 0)
            fusion_rgb = (fusion_rgb - fusion_rgb.min()) / (fusion_rgb.max() - fusion_rgb.min())
            axes[2].imshow(fusion_rgb)
        else:
            axes[2].imshow(fusion_result[0].cpu().numpy(), cmap='viridis')
        axes[2].set_title('融合结果')
        axes[2].axis('off')
        
        # 差异图
        if ms_img.shape == fusion_result.shape:
            diff = torch.abs(ms_img - fusion_result).mean(dim=0).cpu().numpy()
            im = axes[3].imshow(diff, cmap='hot')
            axes[3].set_title('差异图')
            axes[3].axis('off')
            plt.colorbar(im, ax=axes[3])
        else:
            axes[3].text(0.5, 0.5, '形状不匹配\n无法计算差异', 
                        ha='center', va='center', transform=axes[3].transAxes)
            axes[3].set_title('差异图')
            axes[3].axis('off')
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logging.info(f"可视化结果已保存: {save_path}")
        
    except Exception as e:
        logging.error(f"可视化失败: {str(e)}")

def main():
    args = parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 确定设备
    device = torch.device('cuda' if torch.cuda.is_available() and args.cuda else 'cpu')
    logging.info(f"使用设备: {device}")
    
    # 加载影像
    logging.info("正在加载影像...")
    sar_img = load_image(args.sar_path, args.img_size)
    ms_img = load_image(args.ms_path, args.img_size)
    
    if sar_img is None or ms_img is None:
        logging.error("影像加载失败，退出程序")
        return
    
    # 检查通道数
    if sar_img.shape[0] != args.sar_channels:
        logging.warning(f"SAR影像通道数不匹配: 期望{args.sar_channels}, 实际{sar_img.shape[0]}")
    if ms_img.shape[0] != args.ms_channels:
        logging.warning(f"MS影像通道数不匹配: 期望{args.ms_channels}, 实际{ms_img.shape[0]}")
    
    # 加载模型
    logging.info("正在加载模型...")
    model = load_model(
        args.model_path, 
        device,
        img_size=args.img_size,
        patch_size=args.patch_size,
        sar_channels=args.sar_channels,
        ms_channels=args.ms_channels
    )
    
    if model is None:
        logging.error("模型加载失败，退出程序")
        return
    
    # 进行预测
    logging.info("正在进行影像融合预测...")
    results = predict_fusion(model, sar_img, ms_img, device)
    
    # 生成输出文件名
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    base_name = f"fusion_result_{timestamp}"
    
    # 保存融合结果
    fusion_path = os.path.join(args.output_dir, f"{base_name}.tif")
    save_image(results['fusion_result'], fusion_path, args.ms_path)
    
    # 保存特征（如果需要）
    if args.save_features:
        features_path = os.path.join(args.output_dir, f"{base_name}_features.npz")
        np.savez(
            features_path,
            sar_features=results['sar_features'].cpu().numpy(),
            ms_features=results['ms_features'].cpu().numpy(),
            fused_features=results['fused_features'].cpu().numpy()
        )
        logging.info(f"特征已保存: {features_path}")
    
    # 生成可视化（如果需要）
    if args.visualize:
        viz_path = os.path.join(args.output_dir, f"{base_name}_visualization.png")
        visualize_results(sar_img, ms_img, results['fusion_result'], viz_path)
    
    logging.info("预测完成！")
    logging.info(f"融合结果保存在: {fusion_path}")

if __name__ == '__main__':
    main()
