import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, random_split
from torch.optim.lr_scheduler import CosineAnnealingLR, ReduceLROnPlateau
import os
import numpy as np
from utils.loss import FusionLoss
from model import SRN
from utils.dataset import MedicalImageDataset
import argparse
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.FileHandler('training.log'),logging.StreamHandler()])

# 训练参数设置
parser = argparse.ArgumentParser(description='MSRPAN训练脚本')
parser.add_argument('--batch_size', type=int, default=16, help='训练批次大小')
parser.add_argument('--epochs', type=int, default=10, help='训练轮数')
parser.add_argument('--lr', type=float, default=0.0001, help='初始学习率')
parser.add_argument('--weight_decay', type=float, default=1e-5, help='权重衰减参数')
parser.add_argument('--scheduler', type=str, default='cosine', choices=['cosine', 'plateau', 'none'], help='学习率调度器类型')
parser.add_argument('--lr_patience', type=int, default=10, help='ReduceLROnPlateau的patience参数')
parser.add_argument('--lr_factor', type=float, default=0.5, help='ReduceLROnPlateau的factor参数')
parser.add_argument('--min_lr', type=float, default=1e-7, help='最小学习率')
parser.add_argument('--MS_data_dir', type=str, default='./data/train/MS', help='多光谱图像目录')
parser.add_argument('--NTL_data_dir', type=str, default='./data/train/SAR', help='夜间灯光图像目录')
parser.add_argument('--save_dir', type=str, default='./data/model', help='模型保存目录')
parser.add_argument('--up_scale', type=int, default=4, help='上采样倍数')
parser.add_argument('--cuda', action='store_true', help='使用CUDA', default=True)
parser.add_argument('--lambda_rad', type=float, default=0.1, help='辐射一致性保持损失权重')
parser.add_argument('--lambda_perc', type=float, default=0.1, help='感知损失权重')
parser.add_argument('--lambda_struct', type=float, default=0.1, help='结构损失权重')
parser.add_argument('--save_interval', type=int, default=10, help='模型保存间隔(epoch)')
parser.add_argument('--val_ratio', type=float, default=0.1, help='验证集占总数据的比例')
parser.add_argument('--seed', type=int, default=42, help='随机种子，用于数据集划分')

opt = parser.parse_args()

def train():
    # 设置随机种子以确保可重复性
    torch.manual_seed(opt.seed)
    np.random.seed(opt.seed)
    
    # 创建保存目录
    if not os.path.exists(opt.save_dir):
        os.makedirs(opt.save_dir)
    
    # 记录当前时间，用于模型命名
    current_time = datetime.now().strftime('%Y%m%d_%H%M')
    
    # 记录训练配置
    logging.info(f"训练配置: {vars(opt)}")

    # 确定设备
    device = torch.device('cuda' if torch.cuda.is_available() and opt.cuda else 'cpu')
    logging.info(f"使用设备: {device}")

    # 初始化模型
    model = SRN()
    model = model.to(device)
    logging.info(f"模型参数数量: {sum(p.numel() for p in model.parameters() if p.requires_grad)}")

    # 初始化优化器，添加权重衰减
    optimizer = optim.Adam(model.parameters(), lr=opt.lr, weight_decay=opt.weight_decay)
    
    # 初始化学习率调度器
    if opt.scheduler == 'cosine':
        scheduler = CosineAnnealingLR(optimizer, T_max=opt.epochs, eta_min=opt.min_lr)
        logging.info("使用余弦退火学习率调度器")
    elif opt.scheduler == 'plateau':
        scheduler = ReduceLROnPlateau(optimizer, mode='min', factor=opt.lr_factor, 
                                     patience=opt.lr_patience, min_lr=opt.min_lr, verbose=True)
        logging.info(f"使用ReduceLROnPlateau学习率调度器 (因子: {opt.lr_factor}, 耐心值: {opt.lr_patience})")
    else:
        scheduler = None
        logging.info("不使用学习率调度器")

    # 初始化损失函数
    criterion = FusionLoss(
        lambda_rad=opt.lambda_rad,
        lambda_perc=opt.lambda_perc,
        lambda_struct=opt.lambda_struct,
    ).to(device)

    # 加载完整数据集
    full_dataset = MedicalImageDataset(opt.MS_data_dir, opt.NTL_data_dir, opt.up_scale)
    
    # 计算训练集和验证集的大小
    dataset_size = len(full_dataset)
    val_size = int(dataset_size * opt.val_ratio)
    train_size = dataset_size - val_size
    
    # 随机分割数据集
    train_dataset, val_dataset = random_split(
        full_dataset, 
        [train_size, val_size], 
        generator=torch.Generator().manual_seed(opt.seed)
    )
    
    logging.info(f"数据集总大小: {dataset_size}")
    logging.info(f"训练集大小: {train_size}")
    logging.info(f"验证集大小: {val_size}")
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=opt.batch_size, shuffle=True, num_workers=4, pin_memory=True)
    
    val_loader = DataLoader(val_dataset, batch_size=opt.batch_size, shuffle=False, num_workers=4, pin_memory=True
)
    
    # 记录训练历史
    history = {
        'train_loss': [],
        'val_loss': [],
        'learning_rates': []
    }
    
    # 记录最佳模型
    best_val_loss = float('inf')
    
    # 开始训练
    for epoch in range(opt.epochs):
        # 训练阶段
        model.train()
        train_epoch_loss = 0
        
        # 记录开始时间
        start_time = datetime.now()

        for i, (img1, img2, target) in enumerate(train_loader):
            img1, img2, target = img1.to(device), img2.to(device), target.to(device)

            # 清除梯度
            optimizer.zero_grad()

            # 前向传播
            output = model(img1, img2)


            # 计算损失
            loss = criterion(output, target)

            # 反向传播
            loss.backward()

            # 梯度裁剪，防止梯度爆炸
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

            # 更新参数
            optimizer.step()

            train_epoch_loss += loss.item()

            if (i+1) % 10 == 0 or (i+1) == len(train_loader):
                logging.info(f'Epoch [{epoch+1}/{opt.epochs}], Step [{i+1}/{len(train_loader)}], Train Loss: {loss.item():.4f}')

        # 计算平均训练损失
        avg_train_loss = train_epoch_loss / len(train_loader)
        history['train_loss'].append(avg_train_loss)
        
        # 验证阶段
        model.eval()
        val_epoch_loss = 0
        
        with torch.no_grad():
            for i, (img1, img2, target) in enumerate(val_loader):
                img1, img2, target = img1.to(device), img2.to(device), target.to(device)

                      
                # 前向传播
                output = model(img1, img2)

                # 计算损失
                loss = criterion(output, target)
                
                val_epoch_loss += loss.item()
                
                if (i+1) % 5 == 0 or (i+1) == len(val_loader):
                    logging.info(f'Epoch [{epoch+1}/{opt.epochs}], Step [{i+1}/{len(val_loader)}], Val Loss: {loss.item():.4f}')
        
        # 计算平均验证损失
        avg_val_loss = val_epoch_loss / len(val_loader)
        history['val_loss'].append(avg_val_loss)
        
        # 更新学习率
        if scheduler:
            if isinstance(scheduler, ReduceLROnPlateau):
                scheduler.step(avg_val_loss)  # 使用验证损失来调整学习率
            else:
                scheduler.step()
        
        # 记录当前学习率
        current_lr = optimizer.param_groups[0]['lr']
        history['learning_rates'].append(current_lr)
        
        # 计算训练时间
        epoch_time = datetime.now() - start_time
        
        logging.info(f'Epoch [{epoch+1}/{opt.epochs}], '
                    f'训练损失: {avg_train_loss:.4f}, '
                    f'验证损失: {avg_val_loss:.4f}, '
                    f'学习率: {current_lr:.8f}, '
                    f'耗时: {epoch_time}')

        # 保存最佳模型 (基于验证损失)
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            best_model_path = os.path.join(opt.save_dir, f'best_model.pth')
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict() if scheduler else None,
                'train_loss': avg_train_loss,
                'val_loss': avg_val_loss,
                'config': vars(opt),
                'history': history
            }, best_model_path)
            logging.info(f'最佳模型已保存: {best_model_path}, 验证损失: {best_val_loss:.4f}')


    # 保存最终模型
    final_model_path = os.path.join(opt.save_dir, f'model_final.pth')
    torch.save({
        'model_state_dict': model.state_dict(),
        'config': vars(opt),
        'final_train_loss': avg_train_loss,
        'final_val_loss': avg_val_loss,
        'history': history
    }, final_model_path)
    logging.info(f'最终模型已保存: {final_model_path}')
    
    # 打印训练结束信息
    logging.info(f"训练完成! 最佳验证损失: {best_val_loss:.4f}")
    
    # 可以选择在这里绘制损失曲线
    try:
        import matplotlib.pyplot as plt
        plt.figure(figsize=(10, 5))
        
        # 绘制损失曲线
        plt.subplot(1, 2, 1)
        plt.plot(history['train_loss'], label='训练损失')
        plt.plot(history['val_loss'], label='验证损失')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()
        plt.title('训练和验证损失')
        
        # 绘制学习率曲线
        plt.subplot(1, 2, 2)
        plt.plot(history['learning_rates'])
        plt.xlabel('Epoch')
        plt.ylabel('Learning Rate')
        plt.title('学习率变化')
        
        # 保存图表
        plt.tight_layout()
        plt.savefig(os.path.join(opt.save_dir, f'training_curves_{current_time}.png'))
        logging.info(f"训练曲线已保存到 {os.path.join(opt.save_dir, f'training_curves_{current_time}.png')}")
    except Exception as e:
        logging.warning(f"无法绘制训练曲线: {str(e)}")

if __name__ == '__main__':
    train()
