# Fusion-MAE: 跨模态生成式掩码自编码器

## 项目概述

Fusion-MAE是一个基于Vision Transformer (ViT)的跨模态掩码自编码器，专门设计用于SAR和多光谱(MS)影像的融合任务。该模型通过"完形填空"任务强迫模型深度理解并融合两种模态的信息。

## 核心特性

### 🎯 核心思想
- **跨模态掩码学习**: 通过掩码一种模态的75%区域，利用另一种模态的完整信息进行重建
- **双向重建**: 支持SAR→MS和MS→SAR的双向重建任务
- **深度特征融合**: 训练后的编码器能提取深度融合特征用于下游任务

### 🏗️ 模型架构
- **编码器**: 基于ViT的跨模态掩码编码器
- **解码器**: 轻量级Transformer解码器用于重建
- **掩码策略**: 75%高掩码率，强制模型学习泛化特征

### 📊 技术规格
- **输入尺寸**: 256×256 (可配置)
- **Patch大小**: 16×16
- **掩码比例**: 75% (可调整)
- **模型参数**: ~138M
- **支持模态**: SAR (1通道) + 多光谱 (5通道)

## 文件结构

```
SRCNN/
├── fusion_mae.py              # Fusion-MAE核心模型实现
├── fusion_decoder.py          # 融合解码器实现
├── train_fusion_mae.py        # MAE预训练脚本
├── train_fusion_complete.py   # 完整融合训练脚本
├── test_fusion_mae.py         # 功能测试脚本
├── predict_fusion_mae.py      # 影像预测脚本
├── fusion_example.py          # 完整演示脚本
├── utils/
│   ├── dataset.py            # 数据集类 (包含FusionMAEDataset)
│   └── loss.py               # 损失函数 (包含FusionMAELoss)
└── README_Fusion_MAE.md      # 本文档
```

## 快速开始

### 1. 环境要求

```bash
torch>=2.0.0
torchvision
numpy
matplotlib
rasterio
```

### 2. 数据准备

将数据按以下结构组织：
```
data/
├── train/
│   ├── MS/          # 多光谱图像 (.tif格式)
│   └── SAR/         # SAR图像 (.tif格式)
└── val/
    ├── MS/
    └── SAR/
```

### 3. 快速演示

运行完整演示脚本：

```bash
python fusion_example.py
```

### 4. 模型测试

验证模型功能：

```bash
python test_fusion_mae.py
```

### 5. 训练流程

#### 步骤1：MAE预训练

```bash
python train_fusion_mae.py \
    --MS_data_dir ./data/train/MS \
    --SAR_data_dir ./data/train/SAR \
    --batch_size 4 \
    --epochs 200 \
    --lr 1.5e-4 \
    --mask_ratio 0.75
```

#### 步骤2：融合训练

```bash
python train_fusion_complete.py \
    --mae_model_path ./data/model/fusion_mae/best_fusion_mae.pth \
    --MS_data_dir ./data/train/MS \
    --SAR_data_dir ./data/train/SAR \
    --batch_size 4 \
    --epochs 50 \
    --lr 1e-4
```

#### 步骤3：影像预测

```bash
python predict_fusion_mae.py \
    --sar_path ./data/test/sar_image.tif \
    --ms_path ./data/test/ms_image.tif \
    --model_path ./data/model/fusion_mae/best_fusion_mae.pth \
    --fusion_decoder_path ./data/model/fusion_complete/best_fusion_complete.pth \
    --output_dir ./predictions \
    --visualize
```

## 主要参数说明

### 模型参数
- `--img_size`: 输入图像大小 (默认: 256)
- `--patch_size`: Patch大小 (默认: 16)
- `--mask_ratio`: 掩码比例 (默认: 0.75)
- `--encoder_embed_dim`: 编码器嵌入维度 (默认: 768)
- `--encoder_depth`: 编码器层数 (默认: 12)
- `--decoder_embed_dim`: 解码器嵌入维度 (默认: 512)
- `--decoder_depth`: 解码器层数 (默认: 8)

### 训练参数
- `--batch_size`: 批次大小 (默认: 8)
- `--epochs`: 训练轮数 (默认: 100)
- `--lr`: 学习率 (默认: 1.5e-4)
- `--weight_decay`: 权重衰减 (默认: 0.05)
- `--warmup_epochs`: 预热轮数 (默认: 10)

### 损失函数参数
- `--lambda_sar`: SAR重建损失权重 (默认: 1.0)
- `--lambda_ms`: MS重建损失权重 (默认: 1.0)
- `--loss_type`: 损失类型 (默认: mse, 可选: l1, smooth_l1)
- `--norm_pix_loss`: 是否对像素损失归一化

## 使用示例

### 基本使用

```python
import torch
from fusion_mae import create_fusion_mae_model

# 创建模型
model = create_fusion_mae_model(
    img_size=256,
    patch_size=16,
    sar_channels=1,
    ms_channels=5
)

# 创建测试数据
sar_img = torch.randn(2, 1, 256, 256)
ms_img = torch.randn(2, 5, 256, 256)

# 训练模式 - 双模态重建
model.train()
results = model(sar_img, ms_img, mode='both')
print(f"总损失: {results['total_loss'].item():.4f}")

# 推理模式 - 特征提取
model.eval()
with torch.no_grad():
    features = model.extract_features(sar_img, ms_img)
    print(f"特征形状: {features.shape}")
```

### 自定义数据集

```python
from utils.dataset import FusionMAEDataset
from torch.utils.data import DataLoader

# 创建数据集
dataset = FusionMAEDataset(
    MS_data_dir='./data/train/MS',
    SAR_data_dir='./data/train/SAR',
    img_size=256,
    normalize=True,
    augment=True
)

# 创建数据加载器
dataloader = DataLoader(dataset, batch_size=8, shuffle=True)

# 使用数据
for batch in dataloader:
    sar_img = batch['sar']
    ms_img = batch['ms']
    # 训练代码...
    break
```

## 训练策略

### 1. 预训练阶段
- **目标**: 学习跨模态表征
- **任务**: SAR和MS的掩码重建
- **损失**: 重建损失 (MSE/L1)
- **学习率**: 1.5e-4 (带预热)
- **训练轮数**: 100-200 epochs

### 2. 微调阶段 (可选)
- **目标**: 适配特定下游任务
- **任务**: 图像融合/分类等
- **损失**: 任务相关损失
- **学习率**: 1e-5 (较低)

## 模型输出

### 训练模式输出
```python
results = {
    'sar_loss': tensor,      # SAR重建损失
    'ms_loss': tensor,       # MS重建损失  
    'total_loss': tensor,    # 总损失
    'sar_pred': tensor,      # SAR重建结果 [B, 1, H, W]
    'ms_pred': tensor,       # MS重建结果 [B, 5, H, W]
    'sar_mask': tensor,      # SAR掩码 [B, num_patches]
    'ms_mask': tensor        # MS掩码 [B, num_patches]
}
```

### 推理模式输出
```python
features = model.extract_features(sar_img, ms_img)
# 形状: [B, 2*num_patches, embed_dim]
# 包含SAR和MS的融合特征表示
```

## 性能优化建议

### 内存优化
- 使用较小的批次大小 (4-8)
- 启用梯度检查点 (如需要)
- 使用混合精度训练

### 训练加速
- 使用多GPU训练
- 预计算数据集统计信息
- 使用更多数据加载器工作进程

### 模型调优
- 调整掩码比例 (0.5-0.9)
- 尝试不同的损失函数
- 调整学习率调度策略

## 常见问题

### Q: 如何调整掩码比例？
A: 修改 `--mask_ratio` 参数。较高的掩码比例 (0.75-0.9) 强制模型学习更泛化的特征。

### Q: 训练需要多长时间？
A: 在单个RTX 3090上，100 epochs大约需要6-8小时 (取决于数据集大小)。

### Q: 如何使用预训练模型？
A: 加载预训练权重后，使用 `extract_features()` 方法提取特征用于下游任务。

### Q: 支持其他图像尺寸吗？
A: 支持，但需要确保图像尺寸能被patch_size整除。

## 引用

如果您使用了本代码，请引用：

```bibtex
@article{fusion_mae_2024,
  title={Fusion-MAE: Cross-Modal Masked Autoencoder for SAR-Optical Image Fusion},
  author={Your Name},
  journal={Your Journal},
  year={2024}
}
```

## 许可证

本项目采用MIT许可证。详见LICENSE文件。

## 更新日志

- **v1.0.0** (2024-07): 初始版本发布
  - 实现基础Fusion-MAE架构
  - 支持SAR和MS图像的跨模态学习
  - 提供完整的训练和测试脚本
