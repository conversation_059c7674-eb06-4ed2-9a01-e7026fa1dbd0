import torch
import torch.nn as nn
import torch.nn.functional as F


class ChannelAttention(nn.Module):
    def __init__(self, in_channels, reduction_ratio=16):
        super(ChannelAttention, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)

        self.fc = nn.Sequential(
            nn.Conv2d(in_channels, in_channels // reduction_ratio, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels // reduction_ratio, in_channels, 1, bias=False)
        )
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        avg_out = self.fc(self.avg_pool(x))
        max_out = self.fc(self.max_pool(x))
        out = avg_out + max_out
        return self.sigmoid(out)


class SpatialAttention(nn.Module):
    def __init__(self, kernel_size=7):
        super(SpatialAttention, self).__init__()
        self.conv = nn.Conv2d(2, 1, kernel_size=kernel_size, padding=kernel_size // 2, bias=False)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        out = torch.cat([avg_out, max_out], dim=1)
        out = self.conv(out)
        return self.sigmoid(out)


class DualAttentionBlock(nn.Module):
    def __init__(self, in_channels):
        super(DualAttentionBlock, self).__init__()
        self.ca = ChannelAttention(in_channels)
        self.sa = SpatialAttention()

    def forward(self, x):
        x = x * self.ca(x)
        x = x * self.sa(x)
        return x


class ResidualBlock(nn.Module):
    def __init__(self, channels):
        super(ResidualBlock, self).__init__()
        self.conv1 = nn.Conv2d(channels, channels, kernel_size=3, padding=1)
        self.bn1 = nn.BatchNorm2d(channels)
        self.relu = nn.ReLU(inplace=True)
        self.conv2 = nn.Conv2d(channels, channels, kernel_size=3, padding=1)
        self.bn2 = nn.BatchNorm2d(channels)
        self.attention = DualAttentionBlock(channels)

    def forward(self, x):
        residual = x
        out = self.relu(self.bn1(self.conv1(x)))
        out = self.bn2(self.conv2(out))
        out = self.attention(out)
        out += residual
        out = self.relu(out)
        return out


class DenseBlock(nn.Module):
    def __init__(self, in_channels, growth_rate, num_layers):
        super(DenseBlock, self).__init__()
        self.layers = nn.ModuleList()
        for i in range(num_layers):
            self.layers.append(
                nn.Sequential(
                    nn.BatchNorm2d(in_channels + i * growth_rate),
                    nn.ReLU(inplace=True),
                    nn.Conv2d(in_channels + i * growth_rate, growth_rate, kernel_size=3, padding=1, bias=False)
                )
            )

    def forward(self, x):
        features = [x]
        for layer in self.layers:
            new_feature = layer(torch.cat(features, dim=1))
            features.append(new_feature)
        return torch.cat(features, dim=1)


class MultiScaleResBlock(nn.Module):
    def __init__(self, channels):
        super(MultiScaleResBlock, self).__init__()
        self.branch1 = nn.Conv2d(channels, channels // 4, kernel_size=1, bias=False)
        self.branch2 = nn.Sequential(
            nn.Conv2d(channels, channels // 4, kernel_size=1, bias=False),
            nn.Conv2d(channels // 4, channels // 4, kernel_size=3, padding=1, bias=False)
        )
        self.branch3 = nn.Sequential(
            nn.Conv2d(channels, channels // 4, kernel_size=1, bias=False),
            nn.Conv2d(channels // 4, channels // 4, kernel_size=5, padding=2, bias=False)
        )
        self.branch4 = nn.Sequential(
            nn.Conv2d(channels, channels // 4, kernel_size=1, bias=False),
            nn.Conv2d(channels // 4, channels // 4, kernel_size=7, padding=3, bias=False)
        )
        self.conv_merge = nn.Conv2d(channels, channels, kernel_size=1, bias=False)
        self.bn = nn.BatchNorm2d(channels)
        self.relu = nn.ReLU(inplace=True)

    def forward(self, x):
        residual = x

        b1 = self.branch1(x)
        b2 = self.branch2(x)
        b3 = self.branch3(x)
        b4 = self.branch4(x)

        out = torch.cat([b1, b2, b3, b4], dim=1)
        out = self.conv_merge(out)
        out = self.bn(out)

        out += residual
        out = self.relu(out)

        return out


class SRN(nn.Module):
    def __init__(self, ms_channels=5, npp_channels=1, feature_channels=64, num_res_blocks=8):
        super(SRN, self).__init__()

        # 特征提取
        self.ms_conv_initial = nn.Conv2d(ms_channels, feature_channels, kernel_size=3, padding=1)
        self.npp_conv_initial = nn.Conv2d(npp_channels, feature_channels, kernel_size=3, padding=1)

        # MS路径残差块
        self.ms_res_blocks = nn.Sequential(
            *[ResidualBlock(feature_channels) for _ in range(num_res_blocks // 2)]
        )

        # NPP路径残差块
        self.npp_res_blocks = nn.Sequential(
            *[ResidualBlock(feature_channels) for _ in range(num_res_blocks // 2)]
        )

        # 特征融合
        self.fusion_attention = DualAttentionBlock(feature_channels )
        self.fusion_conv = nn.Conv2d(feature_channels , feature_channels, kernel_size=1)

        # 密集块增强特征
        self.dense_block = DenseBlock(feature_channels, growth_rate=32, num_layers=4)
        self.transition = nn.Conv2d(feature_channels , feature_channels, kernel_size=1)

        # 多尺度特征提取
        self.multi_scale_block = MultiScaleResBlock(feature_channels)

        # 上采样部分
        self.upscale = nn.Sequential(
            nn.Conv2d(feature_channels, feature_channels * 4, kernel_size=3, padding=1),
            nn.PixelShuffle(2),
            nn.ReLU(inplace=True),
            nn.Conv2d(feature_channels, feature_channels * 4, kernel_size=3, padding=1),
            nn.PixelShuffle(2),
            nn.ReLU(inplace=True)
        )

        # 最终输出卷积
        self.final_conv = nn.Conv2d(feature_channels, 3, kernel_size=3, padding=1)

    def forward(self, Hf, ms):
        # 特征提取
        ms_feat = self.ms_conv_initial(Hf)

        # 特征增强
        ms_feat = self.ms_res_blocks(ms_feat)

        fused_feat = self.fusion_attention(ms_feat)
        fused_feat = self.fusion_conv(fused_feat)

        dense_feat = self.transition(fused_feat)

        # 多尺度特征提取
        multi_scale_feat = self.multi_scale_block(dense_feat)

        out = self.final_conv(multi_scale_feat)+ms

        return out
