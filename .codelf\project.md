# 项目概述

## 项目名称
SRCNN + Fusion-MAE 跨模态图像融合项目

## 项目描述
本项目基于原有的SRCNN超分辨率重建框架，扩展实现了Fusion-MAE（跨模态生成式掩码自编码器）。该项目专门用于SAR和多光谱影像的融合任务，通过掩码自编码器的"完形填空"机制，强迫模型深度理解并融合两种模态的信息。

## 技术栈
- **深度学习框架**: PyTorch 2.3.0+
- **核心技术**: Vision Transformer (ViT), Masked Autoencoder (MAE)
- **图像处理**: rasterio, torchvision
- **可视化**: matplotlib
- **数据格式**: TIFF格式的遥感影像

## 项目结构

### 核心模块
- `fusion_mae.py`: Fusion-MAE核心模型实现
  - `PatchEmbedding`: 图像分块和嵌入
  - `CrossModalMaskingEncoder`: 跨模态掩码编码器
  - `LightweightDecoder`: 轻量级解码器
  - `FusionMAE`: 主模型类

- `train_fusion_mae.py`: 专用训练脚本
  - 支持双模态重建训练
  - 学习率预热和余弦退火
  - 完整的训练监控和模型保存

- `test_fusion_mae.py`: 综合测试脚本
  - 模型功能验证
  - 掩码机制测试
  - 梯度流检查
  - 重建结果可视化

### 工具模块
- `utils/dataset.py`: 数据集处理
  - `MedicalImageDataset`: 原有数据集类
  - `FusionMAEDataset`: MAE专用数据集类

- `utils/loss.py`: 损失函数
  - `FusionLoss`: 原有融合损失
  - `FusionMAELoss`: MAE重建损失
  - `CombinedLoss`: 混合损失函数

### 原有模块
- `model.py`: 原SRCNN模型实现
- `train.py`: 原训练脚本
- `test.py`: 原测试脚本

## 核心特性

### Fusion-MAE架构特点
1. **跨模态掩码学习**: 75%高掩码率，强制学习泛化特征
2. **双向重建**: 支持SAR→MS和MS→SAR重建
3. **深度特征融合**: 训练后可提取融合特征用于下游任务
4. **轻量级解码器**: 高效的重建网络设计

### 技术创新点
1. **模态类型嵌入**: 区分SAR和MS模态的可学习嵌入
2. **自适应掩码策略**: 支持不同掩码比例的灵活配置
3. **归一化重建损失**: 提高训练稳定性
4. **特征提取接口**: 便于下游任务使用

## 模型规格
- **输入尺寸**: 256×256 (可配置)
- **Patch大小**: 16×16
- **模型参数**: ~138M
- **支持模态**: SAR (1通道) + 多光谱 (5通道)
- **掩码比例**: 75% (可调整)

## 使用场景
1. **预训练阶段**: 学习跨模态表征
2. **特征提取**: 为下游任务提供深度特征
3. **图像融合**: 生成高质量融合影像
4. **研究实验**: 跨模态学习算法验证

## 开发状态
- ✅ 核心模型实现完成
- ✅ 训练脚本开发完成
- ✅ 测试验证通过
- ✅ 文档编写完成
- 🔄 性能优化进行中
- 📋 实际数据验证待进行

## 性能指标
- **测试通过率**: 100% (6/6项测试全部通过)
- **梯度流**: 正常，无梯度消失或爆炸
- **掩码精度**: 与设定比例完全一致
- **内存使用**: 适中，支持批次训练

## 注意事项
1. 需要CUDA支持以获得最佳性能
2. 建议使用较小批次大小 (4-8) 以节省内存
3. 训练数据需要严格配准的SAR-MS影像对
4. 支持数据增强以提高模型泛化能力
