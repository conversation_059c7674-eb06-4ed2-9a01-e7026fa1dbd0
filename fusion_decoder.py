import torch
import torch.nn as nn
import torch.nn.functional as F
import math


class FusionDecoder(nn.Module):
    """
    专用于影像融合的解码器
    将Fusion-MAE提取的深度特征转换为高质量融合影像
    """
    def __init__(
        self,
        feature_dim=768,
        num_patches=256,
        patch_size=16,
        img_size=256,
        output_channels=3,  # RGB输出
        decoder_dim=512,
        decoder_depth=6,
        num_heads=8
    ):
        super().__init__()
        self.feature_dim = feature_dim
        self.num_patches = num_patches
        self.patch_size = patch_size
        self.img_size = img_size
        self.output_channels = output_channels
        
        # 特征投射层
        self.feature_proj = nn.Linear(feature_dim, decoder_dim)
        
        # 跨模态注意力融合层
        self.cross_modal_attention = CrossModalAttention(decoder_dim, num_heads)
        
        # Transformer解码器层
        self.decoder_layers = nn.ModuleList([
            TransformerDecoderLayer(decoder_dim, num_heads)
            for _ in range(decoder_depth)
        ])
        
        # 位置编码
        self.pos_embed = nn.Parameter(torch.zeros(1, num_patches, decoder_dim))
        
        # 上采样网络
        self.upsampling_net = UpsamplingNetwork(
            decoder_dim, patch_size, output_channels
        )
        
        # 残差连接网络（用于细节增强）
        self.detail_enhancement = DetailEnhancementNetwork(output_channels)
        
        self._init_weights()
    
    def _init_weights(self):
        torch.nn.init.trunc_normal_(self.pos_embed, std=0.02)
        for m in self.modules():
            if isinstance(m, nn.Linear):
                torch.nn.init.trunc_normal_(m.weight, std=0.02)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.LayerNorm):
                nn.init.constant_(m.bias, 0)
                nn.init.constant_(m.weight, 1.0)
    
    def forward(self, sar_features, ms_features):
        """
        前向传播
        Args:
            sar_features: SAR特征 [B, num_patches, feature_dim]
            ms_features: MS特征 [B, num_patches, feature_dim]
        Returns:
            fused_image: 融合影像 [B, output_channels, H, W]
        """
        B = sar_features.shape[0]
        
        # 特征投射
        sar_feat = self.feature_proj(sar_features)  # [B, num_patches, decoder_dim]
        ms_feat = self.feature_proj(ms_features)    # [B, num_patches, decoder_dim]
        
        # 跨模态注意力融合
        fused_feat = self.cross_modal_attention(sar_feat, ms_feat)
        
        # 添加位置编码
        fused_feat = fused_feat + self.pos_embed
        
        # 通过Transformer解码器层
        for layer in self.decoder_layers:
            fused_feat = layer(fused_feat)
        
        # 上采样到图像空间
        fused_image = self.upsampling_net(fused_feat)  # [B, output_channels, H, W]
        
        # 细节增强
        fused_image = self.detail_enhancement(fused_image)
        
        return fused_image


class CrossModalAttention(nn.Module):
    """
    跨模态注意力机制
    """
    def __init__(self, dim, num_heads=8):
        super().__init__()
        self.num_heads = num_heads
        self.head_dim = dim // num_heads
        self.scale = self.head_dim ** -0.5
        
        # SAR查询，MS键值
        self.sar_q = nn.Linear(dim, dim)
        self.ms_k = nn.Linear(dim, dim)
        self.ms_v = nn.Linear(dim, dim)
        
        # MS查询，SAR键值
        self.ms_q = nn.Linear(dim, dim)
        self.sar_k = nn.Linear(dim, dim)
        self.sar_v = nn.Linear(dim, dim)
        
        # 融合投射
        self.fusion_proj = nn.Linear(dim * 2, dim)
        self.dropout = nn.Dropout(0.1)
        
    def forward(self, sar_feat, ms_feat):
        B, N, C = sar_feat.shape
        
        # SAR注意MS
        sar_q = self.sar_q(sar_feat).reshape(B, N, self.num_heads, self.head_dim).permute(0, 2, 1, 3)
        ms_k = self.ms_k(ms_feat).reshape(B, N, self.num_heads, self.head_dim).permute(0, 2, 1, 3)
        ms_v = self.ms_v(ms_feat).reshape(B, N, self.num_heads, self.head_dim).permute(0, 2, 1, 3)
        
        attn_sar2ms = (sar_q @ ms_k.transpose(-2, -1)) * self.scale
        attn_sar2ms = F.softmax(attn_sar2ms, dim=-1)
        sar_enhanced = (attn_sar2ms @ ms_v).transpose(1, 2).reshape(B, N, C)
        
        # MS注意SAR
        ms_q = self.ms_q(ms_feat).reshape(B, N, self.num_heads, self.head_dim).permute(0, 2, 1, 3)
        sar_k = self.sar_k(sar_feat).reshape(B, N, self.num_heads, self.head_dim).permute(0, 2, 1, 3)
        sar_v = self.sar_v(sar_feat).reshape(B, N, self.num_heads, self.head_dim).permute(0, 2, 1, 3)
        
        attn_ms2sar = (ms_q @ sar_k.transpose(-2, -1)) * self.scale
        attn_ms2sar = F.softmax(attn_ms2sar, dim=-1)
        ms_enhanced = (attn_ms2sar @ sar_v).transpose(1, 2).reshape(B, N, C)
        
        # 融合两个增强特征
        fused = torch.cat([sar_enhanced, ms_enhanced], dim=-1)
        fused = self.fusion_proj(fused)
        fused = self.dropout(fused)
        
        return fused


class TransformerDecoderLayer(nn.Module):
    """
    Transformer解码器层
    """
    def __init__(self, dim, num_heads, mlp_ratio=4.0):
        super().__init__()
        self.norm1 = nn.LayerNorm(dim)
        self.attn = nn.MultiheadAttention(dim, num_heads, batch_first=True)
        self.norm2 = nn.LayerNorm(dim)
        
        mlp_hidden_dim = int(dim * mlp_ratio)
        self.mlp = nn.Sequential(
            nn.Linear(dim, mlp_hidden_dim),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(mlp_hidden_dim, dim),
            nn.Dropout(0.1)
        )
    
    def forward(self, x):
        # 自注意力
        x_norm = self.norm1(x)
        attn_out, _ = self.attn(x_norm, x_norm, x_norm)
        x = x + attn_out
        
        # MLP
        x = x + self.mlp(self.norm2(x))
        return x


class UpsamplingNetwork(nn.Module):
    """
    上采样网络：将patch特征转换为图像
    """
    def __init__(self, feature_dim, patch_size, output_channels):
        super().__init__()
        self.patch_size = patch_size
        self.output_channels = output_channels
        
        # 将特征转换为patch像素
        self.to_pixels = nn.Linear(feature_dim, patch_size * patch_size * output_channels)
        
        # 卷积细化网络
        self.refine_conv = nn.Sequential(
            nn.Conv2d(output_channels, output_channels * 2, 3, padding=1),
            nn.BatchNorm2d(output_channels * 2),
            nn.ReLU(inplace=True),
            nn.Conv2d(output_channels * 2, output_channels, 3, padding=1),
            nn.BatchNorm2d(output_channels),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, x):
        """
        Args:
            x: [B, num_patches, feature_dim]
        Returns:
            image: [B, output_channels, H, W]
        """
        B, N, _ = x.shape
        
        # 转换为像素值
        pixels = self.to_pixels(x)  # [B, N, patch_size^2 * output_channels]
        
        # 重塑为图像格式
        h = w = int(math.sqrt(N))
        pixels = pixels.reshape(B, h, w, self.patch_size, self.patch_size, self.output_channels)
        pixels = pixels.permute(0, 5, 1, 3, 2, 4)  # [B, C, h, patch_size, w, patch_size]
        image = pixels.reshape(B, self.output_channels, h * self.patch_size, w * self.patch_size)
        
        # 卷积细化
        image = self.refine_conv(image)
        
        return image


class DetailEnhancementNetwork(nn.Module):
    """
    细节增强网络
    """
    def __init__(self, channels):
        super().__init__()
        
        # 多尺度特征提取
        self.conv1 = nn.Conv2d(channels, channels, 3, padding=1)
        self.conv2 = nn.Conv2d(channels, channels, 5, padding=2)
        self.conv3 = nn.Conv2d(channels, channels, 7, padding=3)
        
        # 特征融合
        self.fusion = nn.Conv2d(channels * 3, channels, 1)
        
        # 残差连接
        self.residual = nn.Sequential(
            nn.Conv2d(channels, channels, 3, padding=1),
            nn.BatchNorm2d(channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels, channels, 3, padding=1),
            nn.BatchNorm2d(channels)
        )
        
        self.relu = nn.ReLU(inplace=True)
    
    def forward(self, x):
        # 多尺度特征
        feat1 = F.relu(self.conv1(x))
        feat2 = F.relu(self.conv2(x))
        feat3 = F.relu(self.conv3(x))
        
        # 特征融合
        multi_scale = torch.cat([feat1, feat2, feat3], dim=1)
        fused = self.fusion(multi_scale)
        
        # 残差连接
        residual = self.residual(fused)
        output = self.relu(x + residual)
        
        return output


class CompleteFusionModel(nn.Module):
    """
    完整的融合模型：Fusion-MAE + 融合解码器
    """
    def __init__(self, mae_model, fusion_decoder):
        super().__init__()
        self.mae_model = mae_model
        self.fusion_decoder = fusion_decoder
        
        # 冻结MAE编码器（如果需要）
        # for param in self.mae_model.parameters():
        #     param.requires_grad = False
    
    def forward(self, sar_img, ms_img):
        """
        完整的融合流程
        """
        # 提取深度特征
        with torch.no_grad():  # MAE编码器不需要梯度
            features = self.mae_model.extract_features(sar_img, ms_img)
        
        # 分离SAR和MS特征
        num_patches = features.shape[1] // 2
        sar_features = features[:, :num_patches, :]
        ms_features = features[:, num_patches:, :]
        
        # 融合解码
        fused_image = self.fusion_decoder(sar_features, ms_features)
        
        return fused_image


def create_fusion_model(mae_model_path, device, **fusion_decoder_kwargs):
    """
    创建完整的融合模型
    """
    from fusion_mae import create_fusion_mae_model
    
    # 加载预训练的MAE模型
    mae_model = create_fusion_mae_model()
    checkpoint = torch.load(mae_model_path, map_location=device)
    
    if 'model_state_dict' in checkpoint:
        mae_model.load_state_dict(checkpoint['model_state_dict'])
    else:
        mae_model.load_state_dict(checkpoint)
    
    mae_model.eval()
    
    # 创建融合解码器
    fusion_decoder = FusionDecoder(**fusion_decoder_kwargs)
    
    # 组合完整模型
    complete_model = CompleteFusionModel(mae_model, fusion_decoder)
    
    return complete_model


if __name__ == "__main__":
    # 测试融合解码器
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建测试数据
    B, num_patches, feature_dim = 2, 256, 768
    sar_features = torch.randn(B, num_patches, feature_dim).to(device)
    ms_features = torch.randn(B, num_patches, feature_dim).to(device)
    
    # 创建融合解码器
    fusion_decoder = FusionDecoder(
        feature_dim=feature_dim,
        num_patches=num_patches,
        patch_size=16,
        img_size=256,
        output_channels=3
    ).to(device)
    
    # 测试前向传播
    with torch.no_grad():
        fused_image = fusion_decoder(sar_features, ms_features)
        print(f"融合图像形状: {fused_image.shape}")
        print(f"融合解码器参数数量: {sum(p.numel() for p in fusion_decoder.parameters()):,}")
    
    print("融合解码器测试完成！")
