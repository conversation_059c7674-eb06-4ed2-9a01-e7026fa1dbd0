from __future__ import print_function
import os
import argparse
import torch
from torch.autograd import Variable
from PIL import Image
from torchvision.transforms import ToTensor
from glob import glob
import numpy as np
import rasterio
from rasterio.transform import Affine
from model import SRN
from .dataset import MedicalImageDataset


# 预测数据管理器 - 基于dataset.py的功能进行扩展
class PredictionDataManager:

    def __init__(self, ms_data_dir, npp_data_dir, file_extensions=['tif']):

        self.ms_data_dir = ms_data_dir
        self.npp_data_dir = npp_data_dir
        self.file_extensions = file_extensions

        # 获取文件列表
        self.ms_images, self.npp_images = self._get_image_pairs()

        # 验证数据
        self._validate_data()

    def _get_image_pairs(self):

        ms_images = []
        npp_images = []

        for ext in self.file_extensions:
            ms_pattern = f'*.{ext}'
            npp_pattern = f'*.{ext}'

            ms_images.extend(sorted(glob(os.path.join(self.ms_data_dir, ms_pattern))))
            npp_images.extend(sorted(glob(os.path.join(self.npp_data_dir, npp_pattern))))

        return ms_images, npp_images

    def _validate_data(self):

        if not self.ms_images:
            raise ValueError(f"在 {self.ms_data_dir} 中未找到图像文件")
        if not self.npp_images:
            raise ValueError(f"在 {self.npp_data_dir} 中未找到图像文件")

    def get_image_count(self):

        return min(len(self.ms_images), len(self.npp_images))

    def get_image_paths(self, index):
        """获取指定索引的图像路径对"""
        if index >= self.get_image_count():
            raise IndexError(f"索引 {index} 超出范围")
        return self.ms_images[index], self.npp_images[index]

    def get_all_image_pairs(self):
        """获取所有配对的图像路径"""
        count = self.get_image_count()
        return [(self.ms_images[i], self.npp_images[i]) for i in range(count)]

    def print_summary(self):
        """打印数据摘要信息"""
        print(f"找到 {len(self.ms_images)} 个多光谱图像")
        print(f"找到 {len(self.npp_images)} 个NPP图像")
        print(f"处理的图像对数量: {self.get_image_count()}")


# 增强的图像处理模块 - 复用dataset.py的功能并扩展
class ImageProcessor:

    @staticmethod
    def read_tif_file(file_path):

        with rasterio.open(file_path) as src:
            # 使用与dataset.py相同的读取逻辑
            if src.count > 1:
                # 多波段图像: (C, H, W) -> (H, W, C)
                raw_data = np.transpose(src.read(), (1, 2, 0))
            else:
                # 单波段图像: (H, W)
                raw_data = src.read(1)

            # 返回数据和完整的profile信息
            return raw_data, src.profile.copy()

    @staticmethod
    def read_tif_file_normalized(file_path):

        return MedicalImageDataset.read_tif_file(file_path)


class FusionModel:

    def __init__(self, model_path, use_cuda=True):

        self.device = torch.device('cuda' if torch.cuda.is_available() and use_cuda else 'cpu')
        print(f"使用设备: {self.device}")

        # 支持新旧格式模型加载
        checkpoint = torch.load(model_path, map_location=self.device)

        # 检查是否是新格式的检查点（包含state_dict）
        if isinstance(checkpoint, dict) and "model_state_dict" in checkpoint:
            model = SRN()
            model.load_state_dict(checkpoint["model_state_dict"])
            self.model = model
        else:
            # 兼容旧格式的模型文件
            self.model = checkpoint

        self.model = self.model.to(self.device)
        self.model.eval()

    def fuse_images(self, img1_tensor, img2_tensor):

        img1_tensor = img1_tensor.to(self.device)
        img2_tensor = img2_tensor.to(self.device)

        with torch.no_grad():
            # 执行融合
            fused_tensor = self.model(img1_tensor, img2_tensor)
            return fused_tensor.cpu()


# 医学图像融合应用类 - 重构使用新的数据管理器
class MedicalImageFusion:

    def __init__(self, args):

        self.args = args
        self.processor = ImageProcessor()
        self.model = FusionModel(args.model, args.cuda)

        # 初始化数据管理器
        self.data_manager = PredictionDataManager(
            ms_data_dir=args.MS_data_dir,
            npp_data_dir=args.NPP_data_dir,
            file_extensions=['tif']  # 支持的文件格式
        )

        # 确保输出目录存在
        if not os.path.exists(args.save_folder):
            os.makedirs(args.save_folder)

    def _prepare_image_tensor(self, img_array):

        # 确保有正确的通道维度
        if len(img_array.shape) == 2:
            # 如果是单通道，扩展为 [H, W, 1]
            img_array = np.expand_dims(img_array, axis=2)

        # 转换为张量，与dataset.py保持一致
        tensor = torch.from_numpy(img_array).permute(2, 0, 1).float()

        return tensor

    def _get_high_frequency(self, tensor, kernel_size=5, sigma=1.0):
        """
        提取高频细节（复用dataset.py的逻辑）

        Args:
            tensor: 输入张量
            kernel_size: 高斯核大小
            sigma: 高斯核标准差

        Returns:
            torch.Tensor: 高频细节张量
        """
        # 直接实现dataset.py中的get_high_frequency逻辑
        import torchvision.transforms.functional as TF

        tensor = tensor.float()
        low_freq = TF.gaussian_blur(tensor, kernel_size=kernel_size, sigma=sigma)
        high_freq = tensor - low_freq
        return high_freq

    def run(self):

        # 打印数据摘要
        self.data_manager.print_summary()

        # 获取所有图像对
        image_pairs = self.data_manager.get_all_image_pairs()

        # 处理每个图像对
        for i, (ms_path, npp_path) in enumerate(image_pairs):
            print(f"处理第 {i+1}/{len(image_pairs)} 对图像...")

            ms_img, _ = self.processor.read_tif_file(ms_path)

            # 读取NPP图像
            npp_img, npp_profile = self.processor.read_tif_file(npp_path)

            # 优先使用NPP的元数据，因为它通常包含夜光数据的地理信息
            profile = npp_profile

            # 保存原始图像尺寸，用于后续调整地理坐标
            original_height = npp_img.shape[0] if len(npp_img.shape) == 2 else npp_img.shape[0]
            original_width = npp_img.shape[1] if len(npp_img.shape) == 2 else npp_img.shape[1]

            # 按照dataset.py的方式处理数据
            # 1. 转换为张量（不包含batch维度）
            ms_tensor = self._prepare_image_tensor(ms_img)
            npp_tensor = self._prepare_image_tensor(npp_img)

            # 2. 分别提取高频细节
            ms_high_freq = self._get_high_frequency(ms_tensor)
            npp_high_freq = self._get_high_frequency(npp_tensor)

            # 3. 将两者的高频细节在通道维度上组合
            combined_high_freq = torch.cat((ms_high_freq, npp_high_freq), dim=0)

            # 4. 创建目标张量（用于模型的第二个输入）
            target_ms = ms_tensor[0:3, :, :]  # 取前3个通道

            # 5. 添加batch维度用于模型推理
            combined_high_freq_batch = combined_high_freq.unsqueeze(0)
            target_ms_batch = target_ms.unsqueeze(0)

            # 融合图像 - 使用正确的输入格式
            fused_tensor = self.model.fuse_images(combined_high_freq_batch, target_ms_batch)

            # 保存为TIF格式，保留原始数据类型
            output_path = os.path.join(self.args.save_folder, f"Fused_MS_NPP_{i + 1}.tif")

            # 从张量转换回numpy数组，保持原始值范围
            if fused_tensor.shape[1] == 1:
                # 单通道输出 - 通常用于夜光数据
                fused_np = fused_tensor[0][0].numpy()

                # 确定输出数据类型，优先使用NPP原始数据类型
                if profile:
                    output_dtype = profile.get('dtype', rasterio.float32)
                else:
                    output_dtype = rasterio.float32

                # 创建新的rasterio配置
                if profile:
                    # 计算尺寸比例
                    scale_height = fused_np.shape[0] / original_height
                    scale_width = fused_np.shape[1] / original_width

                    # 如果存在transform参数，则调整它
                    if 'transform' in profile:
                        old_transform = profile['transform']
                        # 创建新的transform，调整像素尺寸但保持地理范围不变
                        new_transform = Affine(
                            old_transform.a / scale_width,  # 缩小x方向的像素尺寸
                            old_transform.b,
                            old_transform.c,  # 左上角x坐标保持不变
                            old_transform.d,
                            old_transform.e / scale_height,  # 缩小y方向的像素尺寸
                            old_transform.f  # 左上角y坐标保持不变
                        )

                        # 更新profile
                        profile.update(
                            height=fused_np.shape[0],
                            width=fused_np.shape[1],
                            transform=new_transform,
                            count=1,
                            dtype=output_dtype
                        )
                    else:
                        # 如果没有transform参数，只更新基本信息
                        profile.update(
                            height=fused_np.shape[0],
                            width=fused_np.shape[1],
                            count=1,
                            dtype=output_dtype
                        )

                    with rasterio.open(output_path, 'w', **profile) as dst:
                        dst.write(fused_np.astype(profile['dtype']), 1)
                else:
                    # 如果没有原始配置，创建基本配置
                    basic_profile = {
                        'driver': 'GTiff',
                        'height': fused_np.shape[0],
                        'width': fused_np.shape[1],
                        'count': 1,
                        'dtype': output_dtype
                    }
                    with rasterio.open(output_path, 'w', **basic_profile) as dst:
                        dst.write(fused_np.astype(output_dtype), 1)
            else:
                # 多通道输出
                channels = fused_tensor.shape[1]
                fused_np = fused_tensor[0].numpy()

                # 确定输出数据类型
                if profile:
                    output_dtype = profile.get('dtype', rasterio.float32)
                else:
                    output_dtype = rasterio.float32

                # 创建rasterio配置
                if profile:
                    # 计算尺寸比例 - 注意多通道输出的维度顺序是 [C, H, W]
                    scale_height = fused_np.shape[1] / original_height
                    scale_width = fused_np.shape[2] / original_width

                    # 如果存在transform参数，则调整它
                    if 'transform' in profile:
                        old_transform = profile['transform']
                        # 创建新的transform，调整像素尺寸但保持地理范围不变
                        new_transform = Affine(
                            old_transform.a / scale_width,  # 缩小x方向的像素尺寸
                            old_transform.b,
                            old_transform.c,  # 左上角x坐标保持不变
                            old_transform.d,
                            old_transform.e / scale_height,  # 缩小y方向的像素尺寸
                            old_transform.f  # 左上角y坐标保持不变
                        )

                        # 更新profile
                        profile.update(
                            height=fused_np.shape[1],
                            width=fused_np.shape[2],
                            transform=new_transform,
                            count=channels,
                            dtype=output_dtype
                        )
                    else:
                        # 如果没有transform参数，只更新基本信息
                        profile.update(
                            height=fused_np.shape[1],
                            width=fused_np.shape[2],
                            count=channels,
                            dtype=output_dtype
                        )

                    with rasterio.open(output_path, 'w', **profile) as dst:
                        for c in range(channels):
                            dst.write(fused_np[c].astype(profile['dtype']), c + 1)
                else:
                    basic_profile = {
                        'driver': 'GTiff',
                        'height': fused_np.shape[1],
                        'width': fused_np.shape[2],
                        'count': channels,
                        'dtype': output_dtype
                    }
                    with rasterio.open(output_path, 'w', **basic_profile) as dst:
                        for c in range(channels):
                            dst.write(fused_np[c].astype(output_dtype), c + 1)

            print(f"已保存融合结果: {output_path}")

        print("所有图像融合完成！")

