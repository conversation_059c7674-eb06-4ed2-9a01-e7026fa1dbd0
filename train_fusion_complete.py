import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, random_split
import os
import numpy as np
import argparse
import logging
from datetime import datetime
import matplotlib.pyplot as plt

# 导入我们的模块
from fusion_mae import create_fusion_mae_model
from fusion_decoder import FusionDecoder, CompleteFusionModel
from utils.dataset import FusionMAEDataset
from utils.loss import FusionLoss

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.FileHandler('fusion_complete_training.log'), logging.StreamHandler()]
)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='完整融合模型训练脚本')
    
    # 数据参数
    parser.add_argument('--MS_data_dir', type=str, default='./data/train/MS', 
                       help='多光谱图像目录')
    parser.add_argument('--SAR_data_dir', type=str, default='./data/train/SAR', 
                       help='SAR图像目录')
    parser.add_argument('--img_size', type=int, default=256, 
                       help='输入图像大小')
    parser.add_argument('--val_ratio', type=float, default=0.1, 
                       help='验证集占总数据的比例')
    
    # 模型参数
    parser.add_argument('--mae_model_path', type=str, required=True,
                       help='预训练的MAE模型路径')
    parser.add_argument('--patch_size', type=int, default=16, 
                       help='patch大小')
    parser.add_argument('--output_channels', type=int, default=3, 
                       help='输出图像通道数（RGB）')
    parser.add_argument('--decoder_dim', type=int, default=512, 
                       help='解码器维度')
    parser.add_argument('--decoder_depth', type=int, default=6, 
                       help='解码器深度')
    
    # 训练参数
    parser.add_argument('--batch_size', type=int, default=4, 
                       help='训练批次大小')
    parser.add_argument('--epochs', type=int, default=20, 
                       help='训练轮数')
    parser.add_argument('--lr', type=float, default=1e-4, 
                       help='初始学习率')
    parser.add_argument('--weight_decay', type=float, default=1e-4, 
                       help='权重衰减参数')
    
    # 损失函数参数
    parser.add_argument('--lambda_rad', type=float, default=1.0, 
                       help='辐射一致性损失权重')
    parser.add_argument('--lambda_perc', type=float, default=0.1, 
                       help='感知损失权重')
    parser.add_argument('--lambda_struct', type=float, default=0.1, 
                       help='结构损失权重')
    
    # 其他参数
    parser.add_argument('--save_dir', type=str, default='./data/model/fusion_complete', 
                       help='模型保存目录')
    parser.add_argument('--save_interval', type=int, default=5, 
                       help='模型保存间隔(epoch)')
    parser.add_argument('--cuda', action='store_true', default=True, 
                       help='使用CUDA')
    parser.add_argument('--seed', type=int, default=42, 
                       help='随机种子')
    parser.add_argument('--num_workers', type=int, default=4, 
                       help='数据加载器工作进程数')
    parser.add_argument('--freeze_mae', action='store_true', 
                       help='是否冻结MAE编码器')
    
    return parser.parse_args()

def create_target_image(sar_img, ms_img):
    """
    创建融合目标图像
    这里使用简单的策略，实际应用中可以使用更复杂的方法
    """
    # 将MS图像上采样到SAR分辨率
    ms_upsampled = torch.nn.functional.interpolate(
        ms_img, 
        size=sar_img.shape[-2:], 
        mode='bilinear', 
        align_corners=False
    )
    
    # 取MS的前3个通道作为RGB目标
    if ms_upsampled.shape[1] >= 3:
        target = ms_upsampled[:, :3, :, :]

    
    return target

def train_epoch(model, dataloader, criterion, optimizer, device, epoch):
    """训练一个epoch"""
    model.train()
    total_loss = 0.0
    num_batches = len(dataloader)
    
    for batch_idx, batch in enumerate(dataloader):
        sar_img = batch['sar'].to(device)
        ms_img = batch['ms'].to(device)
        
        # 创建目标图像
        target = create_target_image(sar_img, ms_img)
        
        # 清除梯度
        optimizer.zero_grad()
        
        # 前向传播
        fusion_output = model(sar_img, ms_img)
        
        # 计算损失
        loss = criterion(fusion_output, target)
        
        # 反向传播
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        
        # 更新参数
        optimizer.step()
        
        # 累计损失
        total_loss += loss.item()
        
        # 打印进度
        if (batch_idx + 1) % 10 == 0 or (batch_idx + 1) == num_batches:
            logging.info(f'Epoch [{epoch+1}], Step [{batch_idx+1}/{num_batches}], '
                        f'Loss: {loss.item():.4f}')
    
    return total_loss / num_batches

def validate_epoch(model, dataloader, criterion, device):
    """验证一个epoch"""
    model.eval()
    total_loss = 0.0
    num_batches = len(dataloader)
    
    with torch.no_grad():
        for batch in dataloader:
            sar_img = batch['sar'].to(device)
            ms_img = batch['ms'].to(device)
            
            # 创建目标图像
            target = create_target_image(sar_img, ms_img)
            
            # 前向传播
            fusion_output = model(sar_img, ms_img)
            
            # 计算损失
            loss = criterion(fusion_output, target)
            
            # 累计损失
            total_loss += loss.item()
    
    return total_loss / num_batches

def save_model(model, optimizer, scheduler, epoch, loss, save_path, config):
    """保存模型"""
    torch.save({
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'fusion_decoder_state_dict': model.fusion_decoder.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'scheduler_state_dict': scheduler.state_dict() if scheduler else None,
        'loss': loss,
        'config': config
    }, save_path)

def save_sample_results(model, dataloader, device, save_dir, epoch):
    """保存样本结果"""
    model.eval()
    
    with torch.no_grad():
        # 获取一个批次的数据
        batch = next(iter(dataloader))
        sar_img = batch['sar'][:1].to(device)  # 只取第一个样本
        ms_img = batch['ms'][:1].to(device)
        
        # 生成融合结果
        fusion_output = model(sar_img, ms_img)
        target = create_target_image(sar_img, ms_img)
        
        # 转换为numpy用于可视化
        sar_np = sar_img[0, 0].cpu().numpy()
        ms_np = ms_img[0, 0].cpu().numpy()
        fusion_np = fusion_output[0].cpu().numpy().transpose(1, 2, 0)
        target_np = target[0].cpu().numpy().transpose(1, 2, 0)
        
        # 归一化到0-1
        fusion_np = (fusion_np - fusion_np.min()) / (fusion_np.max() - fusion_np.min())
        target_np = (target_np - target_np.min()) / (target_np.max() - target_np.min())
        
        # 创建可视化
        fig, axes = plt.subplots(1, 4, figsize=(16, 4))
        
        axes[0].imshow(sar_np, cmap='gray')
        axes[0].set_title('SAR输入')
        axes[0].axis('off')
        
        axes[1].imshow(ms_np, cmap='viridis')
        axes[1].set_title('MS输入')
        axes[1].axis('off')
        
        axes[2].imshow(fusion_np)
        axes[2].set_title('融合输出')
        axes[2].axis('off')
        
        axes[3].imshow(target_np)
        axes[3].set_title('目标图像')
        axes[3].axis('off')
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, f'sample_epoch_{epoch+1}.png'), 
                   dpi=150, bbox_inches='tight')
        plt.close()

def main():
    args = parse_args()
    
    # 设置随机种子
    torch.manual_seed(args.seed)
    np.random.seed(args.seed)
    
    # 创建保存目录
    os.makedirs(args.save_dir, exist_ok=True)
    
    # 记录当前时间
    current_time = datetime.now().strftime('%Y%m%d_%H%M')
    
    # 记录训练配置
    logging.info(f"训练配置: {vars(args)}")
    
    # 确定设备
    device = torch.device('cuda' if torch.cuda.is_available() and args.cuda else 'cpu')
    logging.info(f"使用设备: {device}")
    
    # 创建数据集
    full_dataset = FusionMAEDataset(
        MS_data_dir=args.MS_data_dir,
        SAR_data_dir=args.SAR_data_dir,
        img_size=args.img_size,
        normalize=False,  # 融合任务不需要归一化
        augment=True
    )
    
    # 分割数据集
    dataset_size = len(full_dataset)
    val_size = int(dataset_size * args.val_ratio)
    train_size = dataset_size - val_size
    
    train_dataset, val_dataset = random_split(
        full_dataset, 
        [train_size, val_size], 
        generator=torch.Generator().manual_seed(args.seed)
    )
    
    logging.info(f"数据集总大小: {dataset_size}")
    logging.info(f"训练集大小: {train_size}")
    logging.info(f"验证集大小: {val_size}")
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset, 
        batch_size=args.batch_size, 
        shuffle=True, 
        num_workers=args.num_workers, 
        pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset, 
        batch_size=args.batch_size, 
        shuffle=False, 
        num_workers=args.num_workers, 
        pin_memory=True
    )
    
    # 加载预训练的MAE模型
    mae_model = create_fusion_mae_model()
    mae_checkpoint = torch.load(args.mae_model_path, map_location=device)
    
    if 'model_state_dict' in mae_checkpoint:
        mae_model.load_state_dict(mae_checkpoint['model_state_dict'])
    else:
        mae_model.load_state_dict(mae_checkpoint)
    
    logging.info("成功加载预训练的MAE模型")
    
    # 创建融合解码器
    fusion_decoder = FusionDecoder(
        feature_dim=768,
        num_patches=(args.img_size // args.patch_size) ** 2,
        patch_size=args.patch_size,
        img_size=args.img_size,
        output_channels=args.output_channels,
        decoder_dim=args.decoder_dim,
        decoder_depth=args.decoder_depth
    )
    
    # 创建完整模型
    model = CompleteFusionModel(mae_model, fusion_decoder).to(device)
    
    # 冻结MAE编码器（如果需要）
    if args.freeze_mae:
        for param in model.mae_model.parameters():
            param.requires_grad = False
        logging.info("MAE编码器已冻结")
    
    logging.info(f"模型参数数量: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")
    
    # 创建损失函数
    criterion = FusionLoss(
        lambda_rad=args.lambda_rad,
        lambda_perc=args.lambda_perc,
        lambda_struct=args.lambda_struct
    ).to(device)
    
    # 创建优化器
    optimizer = optim.Adam(
        filter(lambda p: p.requires_grad, model.parameters()), 
        lr=args.lr, 
        weight_decay=args.weight_decay
    )
    
    # 创建学习率调度器
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=args.epochs)
    
    # 训练历史记录
    history = {
        'train_loss': [],
        'val_loss': [],
        'learning_rates': []
    }
    
    best_val_loss = float('inf')
    
    # 开始训练
    logging.info("开始训练完整融合模型...")
    
    for epoch in range(args.epochs):
        start_time = datetime.now()
        
        # 训练
        train_loss = train_epoch(model, train_loader, criterion, optimizer, device, epoch)
        
        # 验证
        val_loss = validate_epoch(model, val_loader, criterion, device)
        
        # 更新学习率
        scheduler.step()
        current_lr = optimizer.param_groups[0]['lr']
        
        # 记录历史
        history['train_loss'].append(train_loss)
        history['val_loss'].append(val_loss)
        history['learning_rates'].append(current_lr)
        
        # 计算训练时间
        epoch_time = datetime.now() - start_time
        
        logging.info(f'Epoch [{epoch+1}/{args.epochs}], '
                    f'训练损失: {train_loss:.4f}, '
                    f'验证损失: {val_loss:.4f}, '
                    f'学习率: {current_lr:.8f}, '
                    f'耗时: {epoch_time}')
        
        # 保存最佳模型
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            best_model_path = os.path.join(args.save_dir, 'best_fusion_complete.pth')
            save_model(model, optimizer, scheduler, epoch, best_val_loss, 
                      best_model_path, vars(args))
            logging.info(f'最佳模型已保存: {best_model_path}, 验证损失: {best_val_loss:.4f}')
        
        # 定期保存模型和样本结果
        if (epoch + 1) % args.save_interval == 0:
            checkpoint_path = os.path.join(args.save_dir, f'fusion_complete_epoch_{epoch+1}.pth')
            save_model(model, optimizer, scheduler, epoch, val_loss, 
                      checkpoint_path, vars(args))
            
            # 保存样本结果
            save_sample_results(model, val_loader, device, args.save_dir, epoch)
            
            logging.info(f'检查点已保存: {checkpoint_path}')
    
    # 保存最终模型
    final_model_path = os.path.join(args.save_dir, f'fusion_complete_final_{current_time}.pth')
    save_model(model, optimizer, scheduler, args.epochs-1, val_loss, 
              final_model_path, vars(args))
    logging.info(f'最终模型已保存: {final_model_path}')
    
    logging.info(f"训练完成! 最佳验证损失: {best_val_loss:.4f}")

if __name__ == '__main__':
    main()
