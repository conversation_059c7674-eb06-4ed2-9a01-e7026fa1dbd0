import os
from glob import glob
import torch
from torch.utils.data import Dataset
import rasterio
import numpy as np
import torchvision.transforms.functional as TF

class MedicalImageDataset(Dataset):
    """
    用于加载多光谱（MS）和SAR/夜光（NTL）影像对的数据集类。
    """
    def __init__(self, MS_data_dir, NTL_data_dir, scale, file_extension='tif'):
        super().__init__()
        self.MS_dir = MS_data_dir
        self.scale = scale
        self.NTL_dir = NTL_data_dir

        # 获取并排序图像文件路径
        self.MS_images = sorted(glob(os.path.join(MS_data_dir, f'*.{file_extension}')))
        self.NTL_images = sorted(glob(os.path.join(NTL_data_dir, f'*.{file_extension}')))
        
        # 确定可用数据对的数量
        self.dataset_size = min(len(self.MS_images), len(self.NTL_images))

        if self.dataset_size == 0:
            raise ValueError("一个或多个图像目录为空，请检查路径。")

        print(f"找到 {len(self.MS_images)} 个多光谱图像。")
        print(f"找到 {len(self.NTL_images)} 个SAR/夜光图像。")
        if len(self.MS_images) != len(self.NTL_images):
            print(f"警告: 图像数量不匹配，将只使用前 {self.dataset_size} 张图像。")
        print(f"可用的匹配数据对数量: {self.dataset_size}")

    def __len__(self):
        return self.dataset_size

    @staticmethod
    def read_tif_file(file_path):
        """读取TIF文件并返回 (H, W, C) 格式的numpy数组。"""
        with rasterio.open(file_path) as src:
            if src.count > 1:
                # 多波段: (C, H, W) -> (H, W, C)
                raw_data = np.transpose(src.read(), (1, 2, 0))
            else:
                # 单波段: (H, W) -> (H, W, 1)
                raw_data = src.read(1)
                raw_data = np.expand_dims(raw_data, axis=-1)
            return raw_data

    def get_high_frequency(self, tensor: torch.Tensor, kernel_size: int = 5, sigma: float = 1.0) -> torch.Tensor:
        """通过减去高斯模糊（低频）分量来提取张量的高频细节。"""
        tensor = tensor.float()
        low_freq = TF.gaussian_blur(tensor, kernel_size=kernel_size, sigma=sigma)
        high_freq = tensor - low_freq
        return high_freq

    def __getitem__(self, idx):
        """获取一个数据样本。"""
        # 读取多光谱图像 (MS)
        ms_img = self.read_tif_file(self.MS_images[idx])
        ms_tensor = torch.from_numpy(ms_img).permute(2, 0, 1).float()

        # 读取SAR/夜光图像 (NTL)
        sar_img = self.read_tif_file(self.NTL_images[idx])
        sar_tensor = torch.from_numpy(sar_img).permute(2, 0, 1).float()


        # 3. 创建一个3通道的目标张量（例如，用于VGG损失）
        ms_tensor = ms_tensor[0:3, :, :]

        # Return the newly created combined high-frequency tensor
        return sar_tensor, ms_tensor, ms_tensor


class FusionMAEDataset(Dataset):
    """
    专为Fusion-MAE设计的数据集类
    支持跨模态掩码自编码器的训练需求
    """
    def __init__(self, MS_data_dir, SAR_data_dir, img_size=256, file_extension='tif',
                 normalize=True, augment=False):
        super().__init__()
        self.MS_dir = MS_data_dir
        self.SAR_dir = SAR_data_dir
        self.img_size = img_size
        self.normalize = normalize
        self.augment = augment

        # 获取并排序图像文件路径
        self.MS_images = sorted(glob(os.path.join(MS_data_dir, f'*.{file_extension}')))
        self.SAR_images = sorted(glob(os.path.join(SAR_data_dir, f'*.{file_extension}')))

        # 确定可用数据对的数量
        self.dataset_size = min(len(self.MS_images), len(self.SAR_images))

        if self.dataset_size == 0:
            raise ValueError("一个或多个图像目录为空，请检查路径。")

        print(f"找到 {len(self.MS_images)} 个多光谱图像。")
        print(f"找到 {len(self.SAR_images)} 个SAR图像。")
        if len(self.MS_images) != len(self.SAR_images):
            print(f"警告: 图像数量不匹配，将只使用前 {self.dataset_size} 张图像。")
        print(f"可用的匹配数据对数量: {self.dataset_size}")


    def __len__(self):
        return self.dataset_size

    @staticmethod
    def read_tif_file(file_path):
        """读取TIF文件并返回 (H, W, C) 格式的numpy数组。"""
        with rasterio.open(file_path) as src:
            if src.count > 1:
                # 多波段: (C, H, W) -> (H, W, C)
                raw_data = np.transpose(src.read(), (1, 2, 0))
            else:
                # 单波段: (H, W) -> (H, W, 1)
                raw_data = src.read(1)
                raw_data = np.expand_dims(raw_data, axis=-1)
            return raw_data

    def _compute_dataset_stats(self):
        """计算数据集的均值和标准差用于归一化"""
        print("正在计算数据集统计信息...")

        # 采样部分数据计算统计信息
        sample_size = min(100, self.dataset_size)
        indices = np.random.choice(self.dataset_size, sample_size, replace=False)

        ms_values = []
        sar_values = []

        for idx in indices:
            # 读取MS图像
            ms_img = self.read_tif_file(self.MS_images[idx])
            ms_values.append(ms_img.flatten())

            # 读取SAR图像
            sar_img = self.read_tif_file(self.SAR_images[idx])
            sar_values.append(sar_img.flatten())

        # 计算统计信息
        ms_all = np.concatenate(ms_values)
        sar_all = np.concatenate(sar_values)

        self.ms_mean = np.mean(ms_all)
        self.ms_std = np.std(ms_all)
        self.sar_mean = np.mean(sar_all)
        self.sar_std = np.std(sar_all)

        print(f"MS - 均值: {self.ms_mean:.4f}, 标准差: {self.ms_std:.4f}")
        print(f"SAR - 均值: {self.sar_mean:.4f}, 标准差: {self.sar_std:.4f}")

    def _normalize_tensor(self, tensor, mean, std):
        """归一化张量"""
        return (tensor - mean) / (std + 1e-8)

    def _resize_tensor(self, tensor, target_size):
        """调整张量大小"""
        if tensor.shape[-2:] != (target_size, target_size):
            tensor = TF.resize(tensor, (target_size, target_size))
        return tensor

    def _augment_data(self, ms_tensor, sar_tensor):
        """数据增强"""
        if not self.augment:
            return ms_tensor, sar_tensor

        # 随机水平翻转
        if torch.rand(1) > 0.5:
            ms_tensor = TF.hflip(ms_tensor)
            sar_tensor = TF.hflip(sar_tensor)

        # 随机垂直翻转
        if torch.rand(1) > 0.5:
            ms_tensor = TF.vflip(ms_tensor)
            sar_tensor = TF.vflip(sar_tensor)

        # 随机旋转
        if torch.rand(1) > 0.5:
            angle = torch.randint(-30, 31, (1,)).item()
            ms_tensor = TF.rotate(ms_tensor, angle)
            sar_tensor = TF.rotate(sar_tensor, angle)

        return ms_tensor, sar_tensor

    def __getitem__(self, idx):
        """获取一个数据样本"""
        # 读取多光谱图像 (MS)
        ms_img = self.read_tif_file(self.MS_images[idx])
        ms_tensor = torch.from_numpy(ms_img).permute(2, 0, 1).float()

        # 读取SAR图像
        sar_img = self.read_tif_file(self.SAR_images[idx])
        sar_tensor = torch.from_numpy(sar_img).permute(2, 0, 1).float()

        # 调整大小
        ms_tensor = self._resize_tensor(ms_tensor, self.img_size)
        sar_tensor = self._resize_tensor(sar_tensor, self.img_size)

        # 数据增强
        ms_tensor, sar_tensor = self._augment_data(ms_tensor, sar_tensor)

        return {
            'sar': sar_tensor,
            'ms': ms_tensor,
            'sar_path': self.SAR_images[idx],
            'ms_path': self.MS_images[idx]
        }