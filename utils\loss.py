import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models


# 定义损失函数
class FusionLoss(nn.Module):
    def __init__(self, lambda_rad=0.1, lambda_perc=0.1, lambda_struct=0.1):
        super(FusionLoss, self).__init__()
        self.mse_loss = nn.MSELoss()
        self.l1_loss = nn.L1Loss()

        # 权重系数
        self.lambda_rad = lambda_rad  # 辐射一致性损失权重
        self.lambda_perc = lambda_perc  # 感知损失权重
        self.lambda_struct = lambda_struct  # 结构损失权重

        # 初始化VGG网络用于感知损失
        vgg = models.vgg19(pretrained=True).features
        self.vgg_features = nn.Sequential()
        for i in range(36):  # 使用VGG前36层
            self.vgg_features.add_module(str(i), vgg[i])

        # 冻结VGG参数
        for param in self.vgg_features.parameters():
            param.requires_grad = False

    def radiation_consistency_loss(self, output, target):

        # 计算降采样后的输出与原始低分辨率图像之间的L1距离
        return self.l1_loss(output, target)

    def perceptual_loss(self, output, target):

        if output.size(1) == 4:
            output = output.repeat(1, 3, 1, 1)
        if target.size(1) == 4:
            target = target.repeat(1, 3, 1, 1)

        # 提取特征
        output_features = self.vgg_features(output)
        target_features = self.vgg_features(target)

        # 计算特征图之间的MSE损失
        return self.mse_loss(output_features, target_features)

    def structural_similarity_loss(self, output, target):

        return 1 - self.ssim(output, target)

    def ssim(self, img1, img2, window_size=11, size_average=True):
        """
        计算SSIM
        """
        # 检查图像通道数
        C1 = 0.01 ** 2
        C2 = 0.03 ** 2

        # 创建高斯窗口
        window = self.create_window(window_size, img1.size(1)).to(img1.device)

        # 计算均值
        mu1 = F.conv2d(img1, window, padding=window_size // 2, groups=img1.size(1))
        mu2 = F.conv2d(img2, window, padding=window_size // 2, groups=img2.size(1))

        mu1_sq = mu1.pow(2)
        mu2_sq = mu2.pow(2)
        mu1_mu2 = mu1 * mu2

        # 计算方差和协方差
        sigma1_sq = F.conv2d(img1 * img1, window, padding=window_size // 2, groups=img1.size(1)) - mu1_sq
        sigma2_sq = F.conv2d(img2 * img2, window, padding=window_size // 2, groups=img2.size(1)) - mu2_sq
        sigma12 = F.conv2d(img1 * img2, window, padding=window_size // 2, groups=img1.size(1)) - mu1_mu2

        # 计算SSIM
        ssim_map = ((2 * mu1_mu2 + C1) * (2 * sigma12 + C2)) / ((mu1_sq + mu2_sq + C1) * (sigma1_sq + sigma2_sq + C2))

        if size_average:
            return ssim_map.mean()
        else:
            return ssim_map.mean(1).mean(1).mean(1)

    def create_window(self, window_size, channel):

        _1D_window = self.gaussian(window_size, 1.5).unsqueeze(1)
        _2D_window = _1D_window.mm(_1D_window.t()).float().unsqueeze(0).unsqueeze(0)
        window = _2D_window.expand(channel, 1, window_size, window_size).contiguous()
        return window

    def gaussian(self, window_size, sigma):

        coords = torch.arange(window_size, dtype=torch.float)
        coords -= window_size // 2

        # 使用torch操作计算高斯值
        gauss = torch.exp(-(coords ** 2) / (2 * sigma ** 2))
        return gauss / gauss.sum()

    def forward(self, output, target):

        if not next(self.vgg_features.parameters()).device == output.device:
            self.vgg_features = self.vgg_features.to(output.device)

        radiation_loss = self.radiation_consistency_loss(output, target)
        perceptual_loss = self.perceptual_loss(output, target)
        ssim_loss = self.structural_similarity_loss(output, target)

        # 总损失
        total_loss = (
                self.lambda_rad * radiation_loss +
                self.lambda_perc * perceptual_loss +
                self.lambda_struct * ssim_loss
        )

        return total_loss
