import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models


# 定义损失函数
class FusionLoss(nn.Module):
    def __init__(self, lambda_rad=0.1, lambda_perc=0.1, lambda_struct=0.1):
        super(FusionLoss, self).__init__()
        self.mse_loss = nn.MSELoss()
        self.l1_loss = nn.L1Loss()

        # 权重系数
        self.lambda_rad = lambda_rad  # 辐射一致性损失权重
        self.lambda_perc = lambda_perc  # 感知损失权重
        self.lambda_struct = lambda_struct  # 结构损失权重

        # 初始化VGG网络用于感知损失
        vgg = models.vgg19(pretrained=True).features
        self.vgg_features = nn.Sequential()
        for i in range(36):  # 使用VGG前36层
            self.vgg_features.add_module(str(i), vgg[i])

        # 冻结VGG参数
        for param in self.vgg_features.parameters():
            param.requires_grad = False

    def radiation_consistency_loss(self, output, target):

        # 计算降采样后的输出与原始低分辨率图像之间的L1距离
        return self.l1_loss(output, target)

    def perceptual_loss(self, output, target):

        if output.size(1) == 4:
            output = output.repeat(1, 3, 1, 1)
        if target.size(1) == 4:
            target = target.repeat(1, 3, 1, 1)

        # 提取特征
        output_features = self.vgg_features(output)
        target_features = self.vgg_features(target)

        # 计算特征图之间的MSE损失
        return self.mse_loss(output_features, target_features)

    def structural_similarity_loss(self, output, target):

        return 1 - self.ssim(output, target)

    def ssim(self, img1, img2, window_size=11, size_average=True):
        """
        计算SSIM
        """
        # 检查图像通道数
        C1 = 0.01 ** 2
        C2 = 0.03 ** 2

        # 创建高斯窗口
        window = self.create_window(window_size, img1.size(1)).to(img1.device)

        # 计算均值
        mu1 = F.conv2d(img1, window, padding=window_size // 2, groups=img1.size(1))
        mu2 = F.conv2d(img2, window, padding=window_size // 2, groups=img2.size(1))

        mu1_sq = mu1.pow(2)
        mu2_sq = mu2.pow(2)
        mu1_mu2 = mu1 * mu2

        # 计算方差和协方差
        sigma1_sq = F.conv2d(img1 * img1, window, padding=window_size // 2, groups=img1.size(1)) - mu1_sq
        sigma2_sq = F.conv2d(img2 * img2, window, padding=window_size // 2, groups=img2.size(1)) - mu2_sq
        sigma12 = F.conv2d(img1 * img2, window, padding=window_size // 2, groups=img1.size(1)) - mu1_mu2

        # 计算SSIM
        ssim_map = ((2 * mu1_mu2 + C1) * (2 * sigma12 + C2)) / ((mu1_sq + mu2_sq + C1) * (sigma1_sq + sigma2_sq + C2))

        if size_average:
            return ssim_map.mean()
        else:
            return ssim_map.mean(1).mean(1).mean(1)

    def create_window(self, window_size, channel):

        _1D_window = self.gaussian(window_size, 1.5).unsqueeze(1)
        _2D_window = _1D_window.mm(_1D_window.t()).float().unsqueeze(0).unsqueeze(0)
        window = _2D_window.expand(channel, 1, window_size, window_size).contiguous()
        return window

    def gaussian(self, window_size, sigma):

        coords = torch.arange(window_size, dtype=torch.float)
        coords -= window_size // 2

        # 使用torch操作计算高斯值
        gauss = torch.exp(-(coords ** 2) / (2 * sigma ** 2))
        return gauss / gauss.sum()

    def forward(self, output, target):

        if not next(self.vgg_features.parameters()).device == output.device:
            self.vgg_features = self.vgg_features.to(output.device)

        radiation_loss = self.radiation_consistency_loss(output, target)
        perceptual_loss = self.perceptual_loss(output, target)
        ssim_loss = self.structural_similarity_loss(output, target)

        # 总损失
        total_loss = (
                self.lambda_rad * radiation_loss +
                self.lambda_perc * perceptual_loss +
                self.lambda_struct * ssim_loss
        )

        return total_loss


class FusionMAELoss(nn.Module):
    """
    专为Fusion-MAE设计的损失函数
    主要包含重建损失，支持SAR和MS的掩码区域重建误差计算
    """
    def __init__(self,
                 lambda_sar=1.0,
                 lambda_ms=1.0,
                 loss_type='mse',
                 norm_pix_loss=True):
        super().__init__()
        self.lambda_sar = lambda_sar  # SAR重建损失权重
        self.lambda_ms = lambda_ms    # MS重建损失权重
        self.loss_type = loss_type    # 损失类型: 'mse', 'l1', 'smooth_l1'
        self.norm_pix_loss = norm_pix_loss  # 是否对像素损失进行归一化

        # 选择基础损失函数
        if loss_type == 'mse':
            self.base_loss = nn.MSELoss(reduction='none')
        elif loss_type == 'l1':
            self.base_loss = nn.L1Loss(reduction='none')
        elif loss_type == 'smooth_l1':
            self.base_loss = nn.SmoothL1Loss(reduction='none')
        else:
            raise ValueError(f"不支持的损失类型: {loss_type}")

    def patchify(self, imgs, patch_size=16):
        """
        将图像转换为patches
        Args:
            imgs: [B, C, H, W]
            patch_size: patch大小
        Returns:
            patches: [B, num_patches, patch_size^2 * C]
        """
        p = patch_size
        assert imgs.shape[2] == imgs.shape[3] and imgs.shape[2] % p == 0

        B, C, H, W = imgs.shape
        h = w = H // p
        x = imgs.reshape(B, C, h, p, w, p)
        x = torch.einsum('nchpwq->nhwpqc', x)
        x = x.reshape(B, h * w, p**2 * C)
        return x

    def compute_reconstruction_loss(self, imgs, pred, mask, patch_size=16):
        """
        计算重建损失 (仅在被掩码的patches上)
        Args:
            imgs: 原始图像 [B, C, H, W]
            pred: 预测的patches [B, num_patches, patch_size^2 * C]
            mask: 掩码 [B, num_patches] (1表示被掩码)
            patch_size: patch大小
        Returns:
            loss: 重建损失
        """
        target = self.patchify(imgs, patch_size)  # [B, num_patches, patch_size^2 * C]

        if self.norm_pix_loss:
            # 对每个patch进行归一化 (类似于MAE原论文)
            mean = target.mean(dim=-1, keepdim=True)
            var = target.var(dim=-1, keepdim=True)
            target = (target - mean) / (var + 1.e-6)**.5

        # 计算损失
        loss = self.base_loss(pred, target)
        loss = loss.mean(dim=-1)  # [B, num_patches] 每个patch的平均损失

        # 只计算被掩码区域的损失
        loss = (loss * mask).sum() / mask.sum()  # 平均损失
        return loss

    def forward(self, model_output):
        """
        前向传播计算总损失
        Args:
            model_output: 模型输出，包含以下键值:
                - 'sar_loss': SAR重建损失 (如果有)
                - 'ms_loss': MS重建损失 (如果有)
                - 'total_loss': 总损失 (如果有)
        Returns:
            total_loss: 总损失
        """
        total_loss = 0.0

        if 'sar_loss' in model_output:
            total_loss += self.lambda_sar * model_output['sar_loss']

        if 'ms_loss' in model_output:
            total_loss += self.lambda_ms * model_output['ms_loss']

        if 'total_loss' in model_output:
            # 如果模型已经计算了总损失，直接使用
            total_loss = model_output['total_loss']

        return total_loss


class CombinedLoss(nn.Module):
    """
    结合传统融合损失和MAE重建损失的混合损失函数
    用于在MAE预训练后进行融合任务的微调
    """
    def __init__(self,
                 lambda_recon=1.0,
                 lambda_fusion=0.1,
                 **fusion_loss_kwargs):
        super().__init__()
        self.lambda_recon = lambda_recon  # 重建损失权重
        self.lambda_fusion = lambda_fusion  # 融合损失权重

        # MAE重建损失
        self.mae_loss = FusionMAELoss()

        # 传统融合损失
        self.fusion_loss = FusionLoss(**fusion_loss_kwargs)

    def forward(self, model_output, fusion_target=None):
        """
        前向传播
        Args:
            model_output: 模型输出 (MAE格式)
            fusion_target: 融合目标图像 (用于融合损失)
        """
        total_loss = 0.0

        # MAE重建损失
        recon_loss = self.mae_loss(model_output)
        total_loss += self.lambda_recon * recon_loss

        # 融合损失 (如果提供了融合目标)
        if fusion_target is not None and 'fusion_output' in model_output:
            fusion_loss = self.fusion_loss(model_output['fusion_output'], fusion_target)
            total_loss += self.lambda_fusion * fusion_loss

        return total_loss
