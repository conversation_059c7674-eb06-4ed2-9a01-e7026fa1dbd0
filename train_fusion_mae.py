import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, random_split
from torch.optim.lr_scheduler import CosineAnnealingLR, ReduceLROnPlateau
import os
import numpy as np
import argparse
import logging
from datetime import datetime
import matplotlib.pyplot as plt

# 导入我们的模块
from fusion_mae import create_fusion_mae_model
from utils.dataset import FusionMAEDataset
from utils.loss import FusionMAELoss

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.FileHandler('fusion_mae_training.log'), logging.StreamHandler()]
)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Fusion-MAE训练脚本')
    
    # 数据参数
    parser.add_argument('--MS_data_dir', type=str, default='./data/train/MS', help='多光谱图像目录')
    parser.add_argument('--SAR_data_dir', type=str, default='./data/train/SAR', help='SAR图像目录')
    parser.add_argument('--img_size', type=int, default=256, help='输入图像大小')
    parser.add_argument('--val_ratio', type=float, default=0.1, help='验证集占总数据的比例')
    
    # 模型参数
    parser.add_argument('--patch_size', type=int, default=16, help='patch大小')
    parser.add_argument('--sar_channels', type=int, default=1, help='SAR图像通道数')
    parser.add_argument('--ms_channels', type=int, default=5, help='多光谱图像通道数')
    parser.add_argument('--encoder_embed_dim', type=int, default=768, help='编码器嵌入维度')
    parser.add_argument('--encoder_depth', type=int, default=12, help='编码器深度')
    parser.add_argument('--encoder_num_heads', type=int, default=12, help='编码器注意力头数')
    parser.add_argument('--decoder_embed_dim', type=int, default=512, help='解码器嵌入维度')
    parser.add_argument('--decoder_depth', type=int, default=8, help='解码器深度')
    parser.add_argument('--decoder_num_heads', type=int, default=16, help='解码器注意力头数')
    parser.add_argument('--mask_ratio', type=float, default=0.75, help='掩码比例')
    
    # 训练参数
    parser.add_argument('--batch_size', type=int, default=8, help='训练批次大小')
    parser.add_argument('--epochs', type=int, default=30, help='训练轮数')
    parser.add_argument('--lr', type=float, default=1.5e-4, help='初始学习率')
    parser.add_argument('--weight_decay', type=float, default=0.05, help='权重衰减参数')
    parser.add_argument('--warmup_epochs', type=int, default=10, help='学习率预热轮数')
    parser.add_argument('--min_lr', type=float, default=1e-6, help='最小学习率')
    
    # 损失函数参数
    parser.add_argument('--lambda_sar', type=float, default=1.0, help='SAR重建损失权重')
    parser.add_argument('--lambda_ms', type=float, default=1.0, help='MS重建损失权重')
    parser.add_argument('--loss_type', type=str, default='mse', choices=['mse', 'l1', 'smooth_l1'], help='损失函数类型')
    parser.add_argument('--norm_pix_loss', action='store_true', help='是否对像素损失进行归一化')
    
    # 其他参数
    parser.add_argument('--save_dir', type=str, default='./data/model/fusion_mae', help='模型保存目录')
    parser.add_argument('--save_interval', type=int, default=10, help='模型保存间隔(epoch)')
    parser.add_argument('--cuda', action='store_true', default=True, help='使用CUDA')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    parser.add_argument('--num_workers', type=int, default=4, help='数据加载器工作进程数')
    parser.add_argument('--augment', action='store_true', help='是否使用数据增强')
    
    return parser.parse_args()

def create_lr_scheduler(optimizer, args):
    """创建学习率调度器"""
    def lr_lambda(epoch):
        if epoch < args.warmup_epochs:
            # 线性预热
            return epoch / args.warmup_epochs
        else:
            # 余弦退火
            progress = (epoch - args.warmup_epochs) / (args.epochs - args.warmup_epochs)
            return 0.5 * (1 + np.cos(np.pi * progress))
    
    return optim.lr_scheduler.LambdaLR(optimizer, lr_lambda)

def train_epoch(model, dataloader, criterion, optimizer, device, epoch):
    """训练一个epoch"""
    model.train()
    total_loss = 0.0
    total_sar_loss = 0.0
    total_ms_loss = 0.0
    num_batches = len(dataloader)
    
    for batch_idx, batch in enumerate(dataloader):
        sar_img = batch['sar'].to(device)
        ms_img = batch['ms'].to(device)
        
        # 清除梯度
        optimizer.zero_grad()
        
        # 前向传播 - 同时进行SAR和MS重建
        results = model(sar_img, ms_img, mode='both')
        
        # 计算损失
        loss = criterion(results)
        
        # 反向传播
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        
        # 更新参数
        optimizer.step()
        
        # 累计损失
        total_loss += loss.item()
        total_sar_loss += results['sar_loss'].item()
        total_ms_loss += results['ms_loss'].item()
        
        # 打印进度
        if (batch_idx + 1) % 10 == 0 or (batch_idx + 1) == num_batches:
            logging.info(f'Epoch [{epoch+1}], Step [{batch_idx+1}/{num_batches}], '
                        f'Loss: {loss.item():.4f}, '
                        f'SAR Loss: {results["sar_loss"].item():.4f}, '
                        f'MS Loss: {results["ms_loss"].item():.4f}')
    
    return {
        'total_loss': total_loss / num_batches,
        'sar_loss': total_sar_loss / num_batches,
        'ms_loss': total_ms_loss / num_batches
    }

def validate_epoch(model, dataloader, criterion, device):
    """验证一个epoch"""
    model.eval()
    total_loss = 0.0
    total_sar_loss = 0.0
    total_ms_loss = 0.0
    num_batches = len(dataloader)
    
    with torch.no_grad():
        for batch in dataloader:
            sar_img = batch['sar'].to(device)
            ms_img = batch['ms'].to(device)
            
            # 前向传播
            results = model(sar_img, ms_img, mode='both')
            
            # 计算损失
            loss = criterion(results)
            
            # 累计损失
            total_loss += loss.item()
            total_sar_loss += results['sar_loss'].item()
            total_ms_loss += results['ms_loss'].item()
    
    return {
        'total_loss': total_loss / num_batches,
        'sar_loss': total_sar_loss / num_batches,
        'ms_loss': total_ms_loss / num_batches
    }

def save_model(model, optimizer, scheduler, epoch, loss, save_path, config):
    """保存模型"""
    torch.save({
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'scheduler_state_dict': scheduler.state_dict() if scheduler else None,
        'loss': loss,
        'config': config
    }, save_path)

def plot_training_curves(history, save_path):
    """绘制训练曲线"""
    epochs = range(1, len(history['train_loss']) + 1)
    
    plt.figure(figsize=(15, 5))
    
    # 总损失
    plt.subplot(1, 3, 1)
    plt.plot(epochs, history['train_loss'], 'b-', label='训练损失')
    plt.plot(epochs, history['val_loss'], 'r-', label='验证损失')
    plt.xlabel('Epoch')
    plt.ylabel('总损失')
    plt.legend()
    plt.title('总损失变化')
    
    # SAR损失
    plt.subplot(1, 3, 2)
    plt.plot(epochs, history['train_sar_loss'], 'b-', label='训练SAR损失')
    plt.plot(epochs, history['val_sar_loss'], 'r-', label='验证SAR损失')
    plt.xlabel('Epoch')
    plt.ylabel('SAR损失')
    plt.legend()
    plt.title('SAR重建损失变化')
    
    # MS损失
    plt.subplot(1, 3, 3)
    plt.plot(epochs, history['train_ms_loss'], 'b-', label='训练MS损失')
    plt.plot(epochs, history['val_ms_loss'], 'r-', label='验证MS损失')
    plt.xlabel('Epoch')
    plt.ylabel('MS损失')
    plt.legend()
    plt.title('MS重建损失变化')
    
    plt.tight_layout()
    plt.savefig(save_path)
    plt.close()

def main():
    args = parse_args()
    
    # 设置随机种子
    torch.manual_seed(args.seed)
    np.random.seed(args.seed)
    
    # 创建保存目录
    os.makedirs(args.save_dir, exist_ok=True)
    
    # 记录当前时间
    current_time = datetime.now().strftime('%Y%m%d_%H%M')
    
    # 记录训练配置
    logging.info(f"训练配置: {vars(args)}")
    
    # 确定设备
    device = torch.device('cuda' if torch.cuda.is_available() and args.cuda else 'cpu')
    logging.info(f"使用设备: {device}")
    
    # 创建数据集
    full_dataset = FusionMAEDataset(
        MS_data_dir=args.MS_data_dir,
        SAR_data_dir=args.SAR_data_dir,
        img_size=args.img_size,
        normalize=True,
        augment=args.augment
    )
    
    # 分割数据集
    dataset_size = len(full_dataset)
    val_size = int(dataset_size * args.val_ratio)
    train_size = dataset_size - val_size
    
    train_dataset, val_dataset = random_split(
        full_dataset, 
        [train_size, val_size], 
        generator=torch.Generator().manual_seed(args.seed)
    )
    
    logging.info(f"数据集总大小: {dataset_size}")
    logging.info(f"训练集大小: {train_size}")
    logging.info(f"验证集大小: {val_size}")
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset, 
        batch_size=args.batch_size, 
        shuffle=True, 
        num_workers=args.num_workers, 
        pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset, 
        batch_size=args.batch_size, 
        shuffle=False, 
        num_workers=args.num_workers, 
        pin_memory=True
    )
    
    # 创建模型
    model = create_fusion_mae_model(
        img_size=args.img_size,
        patch_size=args.patch_size,
        sar_channels=1,
        ms_channels=4,
        encoder_embed_dim=args.encoder_embed_dim,
        encoder_depth=args.encoder_depth,
        encoder_num_heads=args.encoder_num_heads,
        decoder_embed_dim=args.decoder_embed_dim,
        decoder_depth=args.decoder_depth,
        decoder_num_heads=args.decoder_num_heads,
        mask_ratio=args.mask_ratio
    ).to(device)
    
    logging.info(f"模型参数数量: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")
    
    # 创建损失函数
    criterion = FusionMAELoss(
        lambda_sar=args.lambda_sar,
        lambda_ms=args.lambda_ms,
        loss_type=args.loss_type,
        norm_pix_loss=args.norm_pix_loss
    ).to(device)
    
    # 创建优化器
    optimizer = optim.AdamW(model.parameters(), lr=args.lr, weight_decay=args.weight_decay)
    
    # 创建学习率调度器
    scheduler = create_lr_scheduler(optimizer, args)
    
    # 训练历史记录
    history = {
        'train_loss': [],
        'val_loss': [],
        'train_sar_loss': [],
        'val_sar_loss': [],
        'train_ms_loss': [],
        'val_ms_loss': [],
        'learning_rates': []
    }
    
    best_val_loss = float('inf')
    
    # 开始训练
    logging.info("开始训练Fusion-MAE模型...")
    
    for epoch in range(args.epochs):
        start_time = datetime.now()
        
        # 训练
        train_metrics = train_epoch(model, train_loader, criterion, optimizer, device, epoch)
        
        # 验证
        val_metrics = validate_epoch(model, val_loader, criterion, device)
        
        # 更新学习率
        scheduler.step()
        current_lr = optimizer.param_groups[0]['lr']
        
        # 记录历史
        history['train_loss'].append(train_metrics['total_loss'])
        history['val_loss'].append(val_metrics['total_loss'])
        history['train_sar_loss'].append(train_metrics['sar_loss'])
        history['val_sar_loss'].append(val_metrics['sar_loss'])
        history['train_ms_loss'].append(train_metrics['ms_loss'])
        history['val_ms_loss'].append(val_metrics['ms_loss'])
        history['learning_rates'].append(current_lr)
        
        # 计算训练时间
        epoch_time = datetime.now() - start_time
        
        logging.info(f'Epoch [{epoch+1}/{args.epochs}], '
                    f'训练损失: {train_metrics["total_loss"]:.4f}, '
                    f'验证损失: {val_metrics["total_loss"]:.4f}, '
                    f'学习率: {current_lr:.8f}, '
                    f'耗时: {epoch_time}')
        
        # 保存最佳模型
        if val_metrics['total_loss'] < best_val_loss:
            best_val_loss = val_metrics['total_loss']
            best_model_path = os.path.join(args.save_dir, 'best_fusion_mae.pth')
            save_model(model, optimizer, scheduler, epoch, best_val_loss, 
                      best_model_path, vars(args))
            logging.info(f'最佳模型已保存: {best_model_path}, 验证损失: {best_val_loss:.4f}')
        
        # 定期保存模型
        if (epoch + 1) % args.save_interval == 0:
            checkpoint_path = os.path.join(args.save_dir, f'fusion_mae_epoch_{epoch+1}.pth')
            save_model(model, optimizer, scheduler, epoch, val_metrics['total_loss'], 
                      checkpoint_path, vars(args))
            logging.info(f'检查点已保存: {checkpoint_path}')
    
    # 保存最终模型
    final_model_path = os.path.join(args.save_dir, f'fusion_mae_final_{current_time}.pth')
    save_model(model, optimizer, scheduler, args.epochs-1, val_metrics['total_loss'], 
              final_model_path, vars(args))
    logging.info(f'最终模型已保存: {final_model_path}')
    
    # 绘制训练曲线
    try:
        curves_path = os.path.join(args.save_dir, f'training_curves_{current_time}.png')
        plot_training_curves(history, curves_path)
        logging.info(f"训练曲线已保存到: {curves_path}")
    except Exception as e:
        logging.warning(f"无法绘制训练曲线: {str(e)}")
    
    logging.info(f"训练完成! 最佳验证损失: {best_val_loss:.4f}")

if __name__ == '__main__':
    main()
