"""
Fusion-MAE 影像融合完整示例
演示如何使用Fusion-MAE进行真正的影像融合
"""

import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
import os
from datetime import datetime

# 导入我们的模块
from fusion_mae import create_fusion_mae_model
from fusion_decoder import FusionDecoder, CompleteFusionModel
from utils.dataset import FusionMAEDataset
from utils.loss import FusionLoss

def create_sample_data(batch_size=2, img_size=256):
    """
    创建示例数据用于演示
    """
    # 创建模拟的SAR图像（单通道，包含结构信息）
    sar_img = torch.randn(batch_size, 1, img_size, img_size)
    
    # 创建模拟的多光谱图像（5通道，包含光谱信息）
    ms_img = torch.randn(batch_size, 5, img_size, img_size)
    
    return sar_img, ms_img

def demonstrate_mae_pretraining():
    """
    演示MAE预训练过程
    """
    print("=" * 60)
    print("1. MAE预训练演示")
    print("=" * 60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建MAE模型
    mae_model = create_fusion_mae_model(
        img_size=256,
        patch_size=16,
        sar_channels=1,
        ms_channels=5,
        mask_ratio=0.75
    ).to(device)
    
    print(f"MAE模型参数数量: {sum(p.numel() for p in mae_model.parameters()):,}")
    
    # 创建示例数据
    sar_img, ms_img = create_sample_data()
    sar_img, ms_img = sar_img.to(device), ms_img.to(device)
    
    # MAE训练模式：双模态重建
    mae_model.train()
    results = mae_model(sar_img, ms_img, mode='both')
    
    print(f"SAR重建损失: {results['sar_loss'].item():.4f}")
    print(f"MS重建损失: {results['ms_loss'].item():.4f}")
    print(f"总重建损失: {results['total_loss'].item():.4f}")
    
    # 特征提取
    mae_model.eval()
    with torch.no_grad():
        features = mae_model.extract_features(sar_img, ms_img)
        print(f"提取的特征形状: {features.shape}")
    
    return mae_model

def demonstrate_fusion_training(mae_model):
    """
    演示融合训练过程
    """
    print("\n" + "=" * 60)
    print("2. 融合训练演示")
    print("=" * 60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建融合解码器
    fusion_decoder = FusionDecoder(
        feature_dim=768,
        num_patches=256,  # (256//16)^2
        patch_size=16,
        img_size=256,
        output_channels=3,  # RGB输出
        decoder_dim=512,
        decoder_depth=6
    ).to(device)
    
    print(f"融合解码器参数数量: {sum(p.numel() for p in fusion_decoder.parameters()):,}")
    
    # 创建完整融合模型
    complete_model = CompleteFusionModel(mae_model, fusion_decoder).to(device)
    
    # 冻结MAE编码器（通常在融合训练时冻结）
    for param in complete_model.mae_model.parameters():
        param.requires_grad = False
    
    trainable_params = sum(p.numel() for p in complete_model.parameters() if p.requires_grad)
    print(f"可训练参数数量: {trainable_params:,}")
    
    # 创建示例数据和目标
    sar_img, ms_img = create_sample_data()
    sar_img, ms_img = sar_img.to(device), ms_img.to(device)
    
    # 创建目标图像（实际应用中这应该是真实的高质量融合图像）
    target = torch.nn.functional.interpolate(
        ms_img[:, :3, :, :],  # 取前3个通道作为RGB目标
        size=sar_img.shape[-2:],
        mode='bilinear',
        align_corners=False
    )
    
    # 融合训练
    complete_model.train()
    fusion_output = complete_model(sar_img, ms_img)
    
    print(f"融合输出形状: {fusion_output.shape}")
    print(f"目标图像形状: {target.shape}")
    
    # 计算融合损失
    criterion = FusionLoss(lambda_rad=1.0, lambda_perc=0.1, lambda_struct=0.1).to(device)
    loss = criterion(fusion_output, target)
    print(f"融合损失: {loss.item():.4f}")
    
    return complete_model, fusion_output, target

def demonstrate_inference(complete_model):
    """
    演示推理过程
    """
    print("\n" + "=" * 60)
    print("3. 推理演示")
    print("=" * 60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建新的测试数据
    sar_img, ms_img = create_sample_data(batch_size=1)
    sar_img, ms_img = sar_img.to(device), ms_img.to(device)
    
    # 推理模式
    complete_model.eval()
    with torch.no_grad():
        fusion_result = complete_model(sar_img, ms_img)
    
    print(f"输入SAR形状: {sar_img.shape}")
    print(f"输入MS形状: {ms_img.shape}")
    print(f"融合结果形状: {fusion_result.shape}")
    
    # 计算一些质量指标
    mse = torch.mean((fusion_result - sar_img.repeat(1, 3, 1, 1)) ** 2)
    print(f"与SAR的MSE: {mse.item():.4f}")
    
    return fusion_result

def visualize_results(sar_img, ms_img, fusion_output, target=None, save_path=None):
    """
    可视化融合结果
    """
    print("\n" + "=" * 60)
    print("4. 结果可视化")
    print("=" * 60)
    
    # 转换为numpy用于可视化
    sar_np = sar_img[0, 0].cpu().numpy()
    ms_np = ms_img[0, 0].cpu().numpy()  # 只显示第一个通道
    fusion_np = fusion_output[0].cpu().numpy().transpose(1, 2, 0)
    
    # 归一化到0-1
    sar_np = (sar_np - sar_np.min()) / (sar_np.max() - sar_np.min())
    ms_np = (ms_np - ms_np.min()) / (ms_np.max() - ms_np.min())
    fusion_np = (fusion_np - fusion_np.min()) / (fusion_np.max() - fusion_np.min())
    
    # 创建可视化
    if target is not None:
        fig, axes = plt.subplots(1, 4, figsize=(16, 4))
        target_np = target[0].cpu().numpy().transpose(1, 2, 0)
        target_np = (target_np - target_np.min()) / (target_np.max() - target_np.min())
    else:
        fig, axes = plt.subplots(1, 3, figsize=(12, 4))
    
    axes[0].imshow(sar_np, cmap='gray')
    axes[0].set_title('SAR输入')
    axes[0].axis('off')
    
    axes[1].imshow(ms_np, cmap='viridis')
    axes[1].set_title('MS输入 (Ch1)')
    axes[1].axis('off')
    
    axes[2].imshow(fusion_np)
    axes[2].set_title('融合结果')
    axes[2].axis('off')
    
    if target is not None:
        axes[3].imshow(target_np)
        axes[3].set_title('目标图像')
        axes[3].axis('off')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"可视化结果已保存到: {save_path}")
    else:
        plt.show()
    
    plt.close()

def save_model_example(model, save_path):
    """
    保存模型示例
    """
    print("\n" + "=" * 60)
    print("5. 模型保存")
    print("=" * 60)
    
    # 保存完整模型
    torch.save({
        'mae_model_state_dict': model.mae_model.state_dict(),
        'fusion_decoder_state_dict': model.fusion_decoder.state_dict(),
        'complete_model_state_dict': model.state_dict(),
        'timestamp': datetime.now().strftime('%Y%m%d_%H%M%S'),
        'model_info': {
            'mae_params': sum(p.numel() for p in model.mae_model.parameters()),
            'decoder_params': sum(p.numel() for p in model.fusion_decoder.parameters()),
            'total_params': sum(p.numel() for p in model.parameters())
        }
    }, save_path)
    
    print(f"模型已保存到: {save_path}")

def load_model_example(model_path):
    """
    加载模型示例
    """
    print("\n" + "=" * 60)
    print("6. 模型加载")
    print("=" * 60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 加载模型
    checkpoint = torch.load(model_path, map_location=device)
    
    # 重新创建模型
    mae_model = create_fusion_mae_model()
    mae_model.load_state_dict(checkpoint['mae_model_state_dict'])
    
    fusion_decoder = FusionDecoder(
        feature_dim=768,
        num_patches=256,
        patch_size=16,
        img_size=256,
        output_channels=3
    )
    fusion_decoder.load_state_dict(checkpoint['fusion_decoder_state_dict'])
    
    complete_model = CompleteFusionModel(mae_model, fusion_decoder).to(device)
    complete_model.eval()
    
    print("模型加载成功!")
    print(f"模型信息: {checkpoint.get('model_info', '无')}")
    
    return complete_model

def main():
    """
    主函数：完整的融合演示流程
    """
    print("Fusion-MAE 影像融合完整演示")
    print("=" * 60)
    
    # 创建输出目录
    output_dir = "./fusion_demo_results"
    os.makedirs(output_dir, exist_ok=True)
    
    # 1. MAE预训练演示
    mae_model = demonstrate_mae_pretraining()
    
    # 2. 融合训练演示
    complete_model, fusion_output, target = demonstrate_fusion_training(mae_model)
    
    # 3. 推理演示
    fusion_result = demonstrate_inference(complete_model)
    
    # 4. 可视化结果
    sar_img, ms_img = create_sample_data(batch_size=1)
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    sar_img, ms_img = sar_img.to(device), ms_img.to(device)
    
    with torch.no_grad():
        fusion_output = complete_model(sar_img, ms_img)
    
    viz_path = os.path.join(output_dir, f"fusion_demo_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
    visualize_results(sar_img, ms_img, fusion_output, save_path=viz_path)
    
    # 5. 保存模型
    model_path = os.path.join(output_dir, "fusion_demo_model.pth")
    save_model_example(complete_model, model_path)
    
    # 6. 加载模型（演示）
    loaded_model = load_model_example(model_path)
    
    print("\n" + "=" * 60)
    print("演示完成！")
    print("=" * 60)
    print(f"结果保存在: {output_dir}")
    print("\n下一步:")
    print("1. 使用真实数据训练MAE模型: python train_fusion_mae.py")
    print("2. 训练完整融合模型: python train_fusion_complete.py")
    print("3. 进行影像预测: python predict_fusion_mae.py")

if __name__ == "__main__":
    main()
